/* Modern Registration Section Styles - IMA NATCON 2025 */

/* Registration Header Styling */
.registration-header-clean {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
}

.registration-subtitle {
    font-size: 1.6rem;
    color: var(--text-secondary);
    margin: 0.5rem 0 2rem;
    font-weight: 500;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}


.early-bird-tag i {
    margin-right: 0.5rem;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Modern Fees Container */
.modern-fees-container {
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    margin: 1.5rem auto 3rem;
    max-width: 1200px;
    border: 1px solid rgba(0, 0, 0, 0.08);
    transform: translateZ(0);
}

.fees-header-banner {
    background: var(--gradient-primary);
    color: var(--white);
    padding: 1.5rem 2rem;
    text-align: center;
    border-radius: 8px 8px 0 0; /* Rounded top corners */
}

.fees-banner-title {
    font-size: 2.2rem;
    font-weight: 700;
    margin: 0;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.gst-info {
    font-size: 1.4rem;
    font-weight: 400;
    opacity: 0.9;
}

/* Table wrapper with horizontal scroll capability */
.fees-table-wrapper {
    width: 100%;
    overflow-x: auto;
    padding: 0.5rem;
}

.modern-fees-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    font-family: var(--font-family-primary);
}

/* Table Header Styling */
.modern-fees-table thead th {
    position: relative;
    padding: 1.5rem 1rem;
    text-align: center;
    background: #f0f5fa;
    border-bottom: 2px solid #d1e0f0;
    font-weight: 700; /* Increased from 600 to 700 */
    color: #2d3748;
    vertical-align: middle;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.fee-category-column {
    text-align: center !important;
    min-width: 180px;
    font-weight: 700; /* Bold for the 'Category' header */
}

.fee-period-column {
    min-width: 150px;
    text-align: center;
}

.period-date {
    display: block;
    font-size: 1.5rem;
    font-weight: 700; /* Bold font weight for dates */
    padding: 0.2rem 0;
    color: var(--text-primary);
    line-height: 1.4;
    letter-spacing: 0.03em;
}

/* The following styles are kept but commented out in case they need to be reused later
.period-tag {
    display: inline-block;
    padding: 0.3rem 0.8rem;
    font-size: 1rem;
    border-radius: 4px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.early-period {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--primary-green);
}

.regular-period {
    background-color: rgba(46, 89, 144, 0.1);
    color: var(--primary-blue);
}

.late-period {
    background-color: rgba(255, 140, 66, 0.1);
    color: var(--primary-orange);
}

.onsite-period {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color, #dc3545);
}
*/

/* Table Body Styling */
.modern-fees-table tbody td {
    padding: 1.2rem 1rem;
    text-align: center;
    vertical-align: middle;
    border-bottom: 1px solid #e9ecef;
    font-size: 1.5rem;
    font-weight: 400; /* Normal font weight for table data */
    transition: all 0.2s ease;
}

.modern-fees-table tr:last-child td {
    border-bottom: none;
}

.modern-fees-table tr:hover td {
    background-color: rgba(46, 89, 144, 0.03);
}

.fee-category {
    font-weight: 500; /* Reduced from 600 to 500 */
    text-align: left;
    color: var(--text-primary);
}

.fee-note {
    display: block;
    font-size: 1.2rem;
    color: var(--text-secondary);
    font-weight: normal;
    margin-top: 0.3rem;    font-style: italic;
}

.fee-subtext {
    display: inline-block;
    font-size: 1.3rem;
    color: #444;
    font-weight: 500;
    margin-top: 0.5rem;
    padding: 0.3rem 0.8rem;
    background-color: rgba(200, 200, 200, 0.25);
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.08);
    letter-spacing: 0.02em;
}

/* Special styling for IMA member subtexts */
.ima-member-row .fee-subtext {
    background-color: rgba(92, 142, 191, 0.12);
    color: var(--primary-blue-dark, #1a4b7c);
    border: 1px solid rgba(92, 142, 191, 0.2);
    box-shadow: 0 1px 2px rgba(92, 142, 191, 0.1);
}

.fee-amount {
    font-weight: 500; /* Reduced from 700 to 500 to make it normal */
    color: var(--primary-blue);
    text-align: center;
}

/* Delegate Row Styling */
.delegate-row {
    background-color: rgba(46, 89, 144, 0.03);
}

/* IMA Member Row Styling */
.ima-member-row {
    background-color: rgba(46, 89, 144, 0.05);
    position: relative;
}

.ima-member-row:hover td {
    background-color: rgba(46, 89, 144, 0.08);
}

.ima-member-row .fee-category {
    font-weight: 600;
    color: var(--primary-blue);
}

/* Add a subtle separator between Individual and Couple rows */
.ima-member-row:first-of-type {
    border-bottom: 1px dashed rgba(46, 89, 144, 0.2);
}

/* Add a subtle highlight to emphasize this is a two-part row */
.ima-member-row td {
    position: relative;
    transition: all 0.2s ease;
}

/* Left border indicator for IMA member rows */
.ima-member-row td:first-child {
    border-left: 3px solid var(--primary-blue-light, #5c8ebf);
}

.ima-member-row td.fee-amount {
    font-weight: 600;
    color: var(--primary-blue-dark, #1a4b7c);
}

.fee-delegate-category {
    padding: 1rem !important;
}

.delegate-category-title {
    font-weight: 600; /* Reduced weight from 700 to 600 */
    color: var(--primary-blue);
    text-align: left;
    margin-bottom: 0.8rem;
    font-size: 1.6rem;
}

.delegate-type-options {
    display: flex;
    gap: 1.5rem;
    margin-top: 0.5rem;
}

.delegate-type-label {
    font-size: 1.3rem;
    color: #495057;
    font-weight: 500;
    position: relative;
    padding-left: 1.2rem;
}

.delegate-type-label::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--primary-green);
}

.fee-amount-individual,
.fee-amount-couple {
    padding: 0.3rem 0;
    text-align: center;
    font-weight: 500; /* Normal font weight */
}

.fee-amount-individual {
    margin-bottom: 0.5rem;
    position: relative;
}

.fee-amount-individual::after {
    content: '';
    display: block;
    width: 40%;
    height: 1px;
    background: rgba(0, 0, 0, 0.1);
    margin: 0.3rem auto 0;
}

/* Table Footer */
.fees-footer {
    padding: 1.2rem 2rem;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.fees-footnote {
    margin: 0.3rem 0;
    font-size: 1.2rem;
    color: #6c757d;
    font-style: italic;
}

/* Registration CTA */
.registration-cta {
    text-align: center;
    margin: 3rem 0;
    animation: fadeIn 0.8s ease-in-out forwards;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Add subtle animation on page load */
.modern-fees-container {
    animation: slideUp 0.6s ease-out forwards;
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

.register-now-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
    background: var(--gradient-orange);
    color: var(--white);
    padding: 1.2rem 2rem;
    font-size: 1.5rem;
    font-weight: 700;
    border-radius: 50px;
    text-decoration: none;
    box-shadow: 0 8px 16px rgba(255, 140, 66, 0.2);
    transition: all var(--transition-normal);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.register-now-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 20px rgba(255, 140, 66, 0.3);
    background: linear-gradient(120deg, var(--primary-orange-dark), var(--primary-orange));
}

.register-now-btn i {
    font-size: 2rem;
}

.registration-prompt {
    margin-top: 1.5rem;
    font-size: 1.6rem;
    color: #6c757d;
}

/* Animation for IMA member rows */
@keyframes highlightRow {
    0% { background-color: rgba(46, 89, 144, 0.05); }
    50% { background-color: rgba(46, 89, 144, 0.12); }
    100% { background-color: rgba(46, 89, 144, 0.05); }
}

/* Apply animation when table comes into view */
.modern-fees-table.animate-visible .ima-member-row {
    animation: highlightRow 2s ease-in-out;
}

/* Additional styling for Individual and Couple rows */
.ima-individual td:first-child {
    border-left-color: #5c8ebf; /* Lighter blue for individual */
}

.ima-couple td:first-child {
    border-left-color: #1a4b7c; /* Darker blue for couple */
}

/* Subtle styling differences between individual and couple rows */
.ima-individual .fee-subtext {
    background-color: rgba(92, 142, 191, 0.12);
}

.ima-couple .fee-subtext {
    background-color: rgba(26, 75, 124, 0.12);
}

/* Responsive Styling */
@media (max-width: 992px) {
    .fees-banner-title {
        font-size: 2rem;
    }
    
    .period-date {
        font-size: 1.2rem;
    }
    
    .modern-fees-table tbody td {
        font-size: 1.4rem;
    }
    
    .delegate-category-title {
        font-size: 1.5rem;
    }
}

@media (max-width: 768px) {
    .fees-banner-title {
        font-size: 1.8rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .gst-info {
        font-size: 1.2rem;
    }
    
    .modern-fees-table {
        min-width: 700px;
    }
    
    /* Enhance IMA member rows on mobile */
    .ima-member-row td:first-child {
        border-left-width: 2px;
    }
    
    .ima-member-row .fee-subtext {
        font-size: 1.1rem;
        padding: 0.2rem 0.6rem;
    }
    
    .fees-table-wrapper {
        position: relative;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .fees-table-wrapper::after {
        content: '→';
        position: absolute;
        top: 50%;
        right: 10px;
        transform: translateY(-50%);
        color: var(--primary-blue-light);
        font-size: 2rem;
        opacity: 0.6;
        animation: swipeHint 1.5s ease-in-out infinite;
        pointer-events: none;
    }
    
    @keyframes swipeHint {
        0% { transform: translate(0, -50%); opacity: 0; }
        50% { transform: translate(-10px, -50%); opacity: 0.8; }
        100% { transform: translate(0, -50%); opacity: 0; }
    }
    
    .registration-subtitle {
        font-size: 1.4rem;
    }
    
    .early-bird-tag {
        font-size: 1.2rem;
    }
    
    .registration-prompt {
        font-size: 1.4rem;
    }
    
    .register-now-btn {
        padding: 1.1rem 2.5rem;
        font-size: 1.6rem;
    }
}

@media (max-width: 576px) {
    .registration-header-clean {
        margin-bottom: 2rem;
    }
    
    .modern-fees-container {
        margin-bottom: 2rem;
    }
    
    .fees-header-banner {
        padding: 1.2rem;
    }
    
    .fees-footer {
        padding: 1rem;
    }
    
    .registration-cta {
        margin: 2rem 0;
    }
    
    .register-now-btn {
        padding: 1rem 2.2rem;
        font-size: 1.4rem;
    }
    
    .register-now-btn i {
        font-size: 1.6rem;
    }
}

/* E-Badge Download Button */
.ebadge-download-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: var(--white);
    padding: 1.2rem 2rem;
    font-size: 1.5rem;
    font-weight: 700;
    border-radius: 50px;
    text-decoration: none;
    box-shadow: 0 8px 16px rgba(16, 185, 129, 0.2);
    transition: all var(--transition-normal);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.ebadge-download-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.ebadge-download-btn:hover::before {
    left: 100%;
}

.ebadge-download-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 20px rgba(16, 185, 129, 0.3);
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.ebadge-download-btn i {
    font-size: 2rem;
    animation: badgePulse 2s ease-in-out infinite;
}

@keyframes badgePulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* E-Badge Download Section for Registration Page */
.ebadge-download-section {
    text-align: center;
    margin-top: 3rem;
    padding: 2.5rem;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 12px;
    border: 1px solid rgba(16, 185, 129, 0.1);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.05);
}

.ebadge-title {
    font-size: 2.4rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.ebadge-subtitle {
    font-size: 1.6rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    font-weight: 500;
}

/* Responsive E-Badge Button */
@media (max-width: 768px) {
    .ebadge-download-btn {
        padding: 1.1rem 2.5rem;
        font-size: 1.4rem;
    }
    
    .ebadge-download-section {
        padding: 2rem;
        margin-top: 2rem;
    }
    
    .ebadge-title {
        font-size: 2rem;
    }
    
    .ebadge-subtitle {
        font-size: 1.4rem;
    }
}

@media (max-width: 576px) {
    .registration-cta {
        flex-direction: column;
        gap: 1rem;
    }
    
    .ebadge-download-btn {
        padding: 1rem 2rem;
        font-size: 1.3rem;
    }
    
    .ebadge-download-section {
        padding: 1.5rem;
    }
    
    .ebadge-title {
        font-size: 1.8rem;
    }
    
    .ebadge-subtitle {
        font-size: 1.3rem;
    }
}
