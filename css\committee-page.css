/* Elegant Committee Page Styles - Minimalist Premium Design */

/* Main Section */
.elegant-committee-section {
    background: linear-gradient(135deg, 
        #ffffff 0%, 
        #f8fafc 25%, 
        #ffffff 50%, 
        #f1f5f9 75%, 
        #ffffff 100%
    );
    padding: 6rem 0;
    position: relative;
    overflow: hidden;
}

.elegant-committee-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 30%, rgba(46, 89, 144, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(255, 140, 66, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(76, 175, 80, 0.02) 0%, transparent 50%);
    z-index: 1;
}

.elegant-committee-section .container {
    position: relative;
    z-index: 2;
}

/* Section Wrapper */
.committee-section-wrapper {
    margin-bottom: 5rem;
    position: relative;
}

.committee-section-wrapper:last-child {
    margin-bottom: 0;
}

/* Section Header */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
    position: relative;
}

.header-decoration {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
    gap: 1.5rem;
}

.decoration-line {
    height: 2px;
    width: 80px;
    background: linear-gradient(90deg, 
        transparent 0%, 
        var(--primary-blue) 25%, 
        var(--primary-orange) 50%, 
        var(--primary-blue) 75%, 
        transparent 100%
    );
    border-radius: 1px;
}

.decoration-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-orange));
    border-radius: 50%;
    box-shadow: 
        0 10px 25px rgba(46, 89, 144, 0.15),
        0 5px 15px rgba(46, 89, 144, 0.1);
    position: relative;
}

.decoration-icon::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-orange));
    border-radius: 50%;
    z-index: -1;
    opacity: 0.3;
}

.decoration-icon i {
    font-size: var(--font-size-md);
    color: white;
    z-index: 2;
}

.section-title {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-2xl);
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-orange) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    letter-spacing: -0.5px;
}

.section-subtitle {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
    letter-spacing: 1px;
}

/* Chief Patron Container */
.chief-patron-container {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
}

.chief-patron-card {
    max-width: 380px;
    min-height: 320px;
}

/* Committee Grid */
.committee-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
    justify-content: center;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Secretariat Grid */
.secretariat-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    justify-content: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Member Cards */
.member-card {
    background: linear-gradient(145deg, #ffffff, #fafbfc);
    border-radius: 1.5rem;
    box-shadow: 
        0 8px 25px rgba(0, 0, 0, 0.04),
        0 4px 15px rgba(0, 0, 0, 0.02),
        0 2px 8px rgba(0, 0, 0, 0.01);
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 1px solid rgba(255, 255, 255, 0.8);
    overflow: hidden;
    position: relative;
    padding: 2rem 1.5rem;
    text-align: center;
    backdrop-filter: blur(10px);
    width: 100%;
    max-width: 320px;
    margin: 0 auto;
}

.member-card:hover {
    transform: translateY(-12px) scale(1.03);
    box-shadow: 
        0 35px 70px rgba(46, 89, 144, 0.08),
        0 25px 50px rgba(255, 140, 66, 0.06),
        0 15px 30px rgba(76, 175, 80, 0.04),
        0 8px 20px rgba(0, 0, 0, 0.06);
    border-color: rgba(46, 89, 144, 0.2);
    background: linear-gradient(145deg, #ffffff, #f8fafc);
}

/* Card Pattern */
.card-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 25px,
            rgba(46, 89, 144, 0.01) 25px,
            rgba(46, 89, 144, 0.01) 50px
        );
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.member-card:hover .card-pattern {
    opacity: 1;
}

/* Member Photo Section */
.member-photo-section {
    position: relative;
    z-index: 3;
    margin-bottom: 1.5rem;
}

.photo-border {
    position: relative;
    width: 100px;
    height: 100px;
    margin: 0 auto;
}

.photo-frame {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-orange));
    padding: 4px;
    box-shadow: 
        0 10px 25px rgba(46, 89, 144, 0.15),
        0 5px 15px rgba(46, 89, 144, 0.1);
    transition: all 0.3s ease;
}

.member-card:hover .photo-frame {
    transform: scale(1.05);
    box-shadow: 
        0 15px 35px rgba(46, 89, 144, 0.2),
        0 10px 25px rgba(46, 89, 144, 0.15);
}

.member-photo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    transition: all 0.3s ease;
    background: #f8fafc;
}

.member-card:hover .member-photo {
    transform: scale(1.02);
}

/* Member Info */
.member-info {
    position: relative;
    z-index: 3;
}

.member-name {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-md);
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 0.8rem;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.member-card:hover .member-name {
    color: var(--primary-orange);
    transform: translateY(-2px);
}

.member-designation {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    font-weight: 500;
    line-height: 1.4;
    margin: 0;
}

/* Chief Patron Special Styling */
.chief-patron-card {
    position: relative;
    border: 2px solid transparent;
    background-clip: padding-box;
}

.chief-patron-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-orange), var(--primary-green));
    border-radius: 1.5rem;
    z-index: -1;
    opacity: 0.6;
}

.chief-patron-card .photo-border {
    width: 110px;
    height: 110px;
}

.chief-patron-card .member-name {
    font-size: var(--font-size-lg);
    margin-bottom: 1rem;
}

.chief-patron-card .member-designation {
    font-size: var(--font-size-md);
    font-weight: 600;
    background: linear-gradient(135deg, var(--primary-orange), var(--primary-blue));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Secretariat Cards */
.secretariat-card {
    min-height: 280px;
    padding: 1.5rem 1rem;
}

.secretariat-card .photo-border {
    width: 90px;
    height: 90px;
}

.secretariat-card .member-name {
    font-size: var(--font-size-base);
}

.secretariat-card .member-designation {
    font-size: var(--font-size-sm);
}

/* Responsive Design */

/* Large Desktop */
@media (max-width: 1400px) {
    .committee-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
        max-width: 1200px;
    }
    
    .secretariat-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.8rem;
        max-width: 1000px;
    }
}

/* Desktop */
@media (max-width: 1200px) {
    .elegant-committee-section {
        padding: 5rem 0;
    }
    
    .section-title {
        font-size: var(--font-size-xl);
    }
    
    .committee-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
        max-width: 1000px;
    }
    
    .secretariat-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
        max-width: 900px;
    }
}

/* Tablet */
@media (max-width: 768px) {
    .elegant-committee-section {
        padding: 4rem 0;
    }
    
    .committee-section-wrapper {
        margin-bottom: 4rem;
    }
    
    .section-header {
        margin-bottom: 3rem;
    }
    
    .decoration-icon {
        width: 50px;
        height: 50px;
    }
    
    .decoration-icon i {
        font-size: var(--font-size-sm);
    }
    
    .decoration-line {
        width: 60px;
    }
    
    .section-title {
        font-size: var(--font-size-lg);
    }
    
    .section-subtitle {
        font-size: var(--font-size-base);
    }
    
    .committee-grid,
    .secretariat-grid {
        grid-template-columns: repeat(2, 1fr);
        max-width: 700px;
        gap: 2rem;
    }
    
    .member-card {
        padding: 2rem 1.5rem;
        border-radius: 1.2rem;
    }
    
    .photo-border {
        width: 90px;
        height: 90px;
    }
    
    .chief-patron-card .photo-border {
        width: 100px;
        height: 100px;
    }
    
    .member-name {
        font-size: var(--font-size-base);
    }
    
    .chief-patron-card .member-name {
        font-size: var(--font-size-md);
    }
}

/* Mobile */
@media (max-width: 480px) {
    .elegant-committee-section {
        padding: 3rem 0;
    }
    
    .committee-section-wrapper {
        margin-bottom: 3rem;
    }
    
    .section-header {
        margin-bottom: 2.5rem;
    }
    
    .decoration-icon {
        width: 45px;
        height: 45px;
    }
    
    .decoration-icon i {
        font-size: var(--font-size-base);
    }
    
    .decoration-line {
        width: 40px;
    }
    
    .section-title {
        font-size: var(--font-size-md);
    }
    
    .section-subtitle {
        font-size: var(--font-size-xs);
    }
    
    .committee-grid,
    .secretariat-grid {
        grid-template-columns: 1fr;
        max-width: 350px;
        gap: 1.5rem;
    }
    
    .member-card {
        padding: 1.5rem 1rem;
        border-radius: 1rem;
    }
    
    .photo-border {
        width: 80px;
        height: 80px;
    }
    
    .chief-patron-card .photo-border {
        width: 90px;
        height: 90px;
    }
    
    .secretariat-card .photo-border {
        width: 75px;
        height: 75px;
    }
    
    .member-name {
        font-size: var(--font-size-sm);
    }
    
    .chief-patron-card .member-name {
        font-size: var(--font-size-base);
    }
    
    .member-designation {
        font-size: var(--font-size-xs);
    }
    
    .chief-patron-card .member-designation {
        font-size: var(--font-size-sm);
    }
    
    .secretariat-card .member-designation {
        font-size: var(--font-size-xs);
    }
}

/* Small Mobile */
@media (max-width: 360px) {
    .committee-grid,
    .secretariat-grid {
        grid-template-columns: 1fr;
        max-width: 300px;
    }
    
    .member-card {
        margin: 0 0.5rem;
        padding: 1.2rem 0.8rem;
    }
    
    .photo-border {
        width: 75px;
        height: 75px;
    }
    
    .chief-patron-card .photo-border {
        width: 85px;
        height: 85px;
    }
    
    .member-name {
        font-size: var(--font-size-xs);
    }
    
    .chief-patron-card .member-name {
        font-size: var(--font-size-sm);
    }
}

/* Enhanced Animations */
.member-card {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease-out forwards;
}

.member-card:nth-child(1) { animation-delay: 0.1s; }
.member-card:nth-child(2) { animation-delay: 0.2s; }
.member-card:nth-child(3) { animation-delay: 0.3s; }
.member-card:nth-child(4) { animation-delay: 0.4s; }
.member-card:nth-child(5) { animation-delay: 0.5s; }
.member-card:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Smooth Scroll */
html {
    scroll-behavior: smooth;
}

/* Accessibility */
.member-card:focus-within {
    outline: 3px solid var(--primary-blue);
    outline-offset: 5px;
    transform: translateY(-5px) scale(1.02);
}

/* Print Styles */
@media print {
    .elegant-committee-section {
        background: white !important;
        padding: 2rem 0 !important;
    }
    
    .member-card {
        box-shadow: none !important;
        border: 1px solid #ccc !important;
        break-inside: avoid;
        margin-bottom: 1rem !important;
    }
    
    .card-pattern {
        display: none !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .member-card {
        border: 2px solid #000;
        background: #fff;
    }
    
    .member-name {
        color: #000 !important;
    }
    
    .member-designation {
        color: #333 !important;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .member-card:hover {
        transform: none !important;
    }
}
