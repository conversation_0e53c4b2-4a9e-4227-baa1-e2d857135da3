/* ========================================
   Modern Places to Visit Page - UI/UX Optimized
   ======================================== */

/* Hero Section Enhancement */
.global-page-hero {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-orange) 100%);
    position: relative;
    overflow: hidden;
}

.global-page-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.5;
}

/* About Section - Minimalist Design */
.about-section {
    padding: 8rem 0;
    background: var(--white);
    position: relative;
    overflow: hidden;
}

.about-container {
    max-width: 100rem;
    margin: 0 auto;
    padding: 0 2rem;
}

.about-header {
    text-align: center;
    margin-bottom: 6rem;
}

/* Section titles use common styling from main CSS */

.about-content {
    display: grid;
    gap: 4rem;
    max-width: 90rem;
    margin: 0 auto;
}

.about-text {
    font-size: 1.8rem;
    line-height: 1.8;
    color: var(--text-secondary);
    text-align: justify;
    position: relative;
    padding: 3rem;
    background: var(--gray-50);
    border-radius: 2rem;
    border-left: 0.5rem solid var(--primary-orange);
    box-shadow: 0 4px 20px rgba(0,0,0,0.05);
    transition: transform 0.3s ease;
}

.about-text:hover {
    transform: translateY(-5px);
}

/* Places Grid - Modern Card Layout */
.places-section {
    padding: 8rem 0;
    background: linear-gradient(180deg, var(--gray-50) 0%, var(--white) 100%);
    position: relative;
    overflow: hidden;
}

/* Mandala Background Div */
.about-section .mandala-background {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 70rem;
    height: 70rem;
    background: url('/assets/mandala/preview.png') no-repeat center center, 
                radial-gradient(circle, rgba(46, 89, 144, 0.1) 0%, transparent 70%);
    background-size: contain;
    opacity: 0.3;
    animation: rotateMandala 60s linear infinite;
    z-index: 1;
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes rotateMandala {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

.places-header {
    text-align: center;
    margin-bottom: 6rem;
    position: relative;
    z-index: 2;
}

/* Places title uses common section-title styling */

.places-subtitle {
    font-size: 2rem;
    color: var(--text-secondary);
    max-width: 70rem;
    margin: 0 auto;
    line-height: 1.6;
}

/* Filter Tabs */
.category-filters {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 5rem;
    position: relative;
    z-index: 2;
}

.filter-btn {
    padding: 1.2rem 2.4rem;
    background: var(--white);
    color: var(--text-secondary);
    border: 2px solid var(--border-color);
    border-radius: 3rem;
    font-size: 1.4rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.filter-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: left 0.3s ease;
    z-index: -1;
}

.filter-btn:hover::before,
.filter-btn.active::before {
    left: 0;
}

.filter-btn:hover,
.filter-btn.active {
    color: var(--white);
    /* border-color: var(--primary-blue); */
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(46, 89, 144, 0.3);
}

/* Places Grid */
.places-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(40rem, 1fr));
    gap: 3rem;
    max-width: 140rem;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
    z-index: 2;
}

/* Modern Place Cards */
.place-card {
    background: var(--white);
    border-radius: 2rem;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0,0,0,0.08);
    transition: 
        opacity 0.3s ease,
        transform 0.3s ease,
        box-shadow 0.3s ease;
    position: relative;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    opacity: 1;
    transform: translateY(0);
}

.place-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-modern);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.place-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
}

.place-card:hover::before {
    transform: scaleX(1);
}

.place-image-container {
    position: relative;
    height: 24rem;
    overflow: hidden;
}

.place-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s ease;
}

.place-card:hover .place-image {
    transform: scale(1.1);
}

.place-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, transparent 0%, rgba(0,0,0,0.7) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.place-card:hover .place-overlay {
    opacity: 1;
}

.place-category-badge {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    background: rgba(255,255,255,0.95);
    color: var(--primary-blue);
    padding: 0.8rem 1.5rem;
    border-radius: 2rem;
    font-size: 1.2rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
    transform: translateY(-10px);
    opacity: 0;
    transition: all 0.4s ease;
}

.place-card:hover .place-category-badge {
    transform: translateY(0);
    opacity: 1;
}

.place-content {
    padding: 2.5rem;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    justify-content: space-between;
}

.place-title {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 1.5rem;
    line-height: 1.3;
}

.place-description {
    font-size: 1.5rem;
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 2rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.place-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.place-distance {
    color: var(--text-light);
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.learn-more-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.8rem;
    color: var(--primary-orange);
    font-weight: 600;
    font-size: 1.4rem;
    text-decoration: none;
    transition: all 0.3s ease;
}

.learn-more-btn:hover {
    color: var(--primary-blue);
    transform: translateX(5px);
}

/* Loading Animation */
.place-card {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.6s ease forwards;
}

.place-card:nth-child(1) { animation-delay: 0.1s; }
.place-card:nth-child(2) { animation-delay: 0.2s; }
.place-card:nth-child(3) { animation-delay: 0.3s; }
.place-card:nth-child(4) { animation-delay: 0.4s; }
.place-card:nth-child(5) { animation-delay: 0.5s; }
.place-card:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* CTA Section */
.cta-section {
    padding: 8rem 0;
    background: var(--gradient-modern);
    text-align: center;
    color: var(--white);
}

/* CTA title override for white color */
.cta-section .section-title {
    color: var(--white) !important;
    background: none !important;
    -webkit-background-clip: initial !important;
    background-clip: initial !important;
    -webkit-text-fill-color: var(--white) !important;
    animation: none !important;
}

.cta-description {
    font-size: 1.8rem;
    margin-bottom: 3rem;
    max-width: 60rem;
    margin-left: auto;
    margin-right: auto;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem 3rem;
    background: var(--white);
    color: var(--primary-blue);
    font-size: 1.6rem;
    font-weight: 600;
    text-decoration: none;
    border-radius: 3rem;
    transition: all 0.3s ease;
    box-shadow: 0 8px 30px rgba(0,0,0,0.2);
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.3);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .places-grid {
        grid-template-columns: repeat(auto-fill, minmax(35rem, 1fr));
        gap: 2.5rem;
    }
    
    .mandala-background {
        width: 60rem;
        height: 60rem;
        top: -5rem;
    }
    
    /* Section titles use responsive styling from main CSS */
}

@media (max-width: 768px) {
    .about-section,
    .places-section {
        padding: 6rem 0;
    }
    
    .places-grid {
        grid-template-columns: repeat(auto-fill, minmax(30rem, 1fr));
        gap: 2rem;
        padding: 0 1.5rem;
    }
}

@media (max-width: 600px) {
    .places-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .mandala-background {
        width: 45rem;
        height: 45rem;
        top: 0;
        opacity: 0.06;
    }
    
    /* Section titles responsive styling handled by main CSS */
    
    .about-text {
        padding: 2rem;
        font-size: 1.6rem;
    }
    
    .category-filters {
        gap: 0.8rem;
    }
    
    .filter-btn {
        padding: 1rem 2rem;
        font-size: 1.3rem;
    }
    
    .place-content {
        padding: 2rem;
    }
    
    .place-title {
        font-size: 2rem;
    }
    
    /* CTA title responsive styling handled by main CSS */
}

@media (max-width: 480px) {
    .about-container,
    .places-grid {
        padding: 0 1rem;
        grid-template-columns: 1fr;
    }
    
    .mandala-background {
        width: 35rem;
        height: 35rem;
        top: 5rem;
        opacity: 0.05;
    }
    
    .category-filters {
        flex-direction: column;
        align-items: center;
    }
    
    .filter-btn {
        width: 100%;
        max-width: 25rem;
    }
    
    .place-meta {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
} 