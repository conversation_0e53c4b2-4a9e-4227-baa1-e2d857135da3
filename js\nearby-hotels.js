/**
 * IMA NATCON 2025 - Nearby Hotels JavaScript
 * Handles hotel data, filtering, sorting, and dynamic content
 */

(function() {
    'use strict';

    // ========================================
    // Hotel Data
    // ========================================

    const hotelsData = [
        {
            id: 1,
            name: "Holiday Inn Express – Prahlad Nagar",
            location: "Prahlad Nagar, Ahmedabad",
            distanceFromAirport: "17–20 km (~30–45 min)",
            distanceFromClub: "5.8 km (~15–20 min)",
            rating: "4.3",
            category: "comfort",
            image: "assets/hotels/Holiday Inn Express – Prahlad Nagar.webp",
            features: ["Free WiFi", "Complimentary Breakfast", "Gym", "Restaurant"],
            bookingUrl: "https://www.ihg.com/holidayinnexpress/hotels/us/en/gujarat/gjupn/hoteldetail?cm_mmc=GoogleMaps-_-EX-_-IN-_-GJUPN",
            distanceValue: 5.8
        },
        {
            id: 2,
            name: "Crowne Plaza Ahmedabad City Centre",
            location: "Prahlad Nagar, Ahmedabad",
            distanceFromAirport: "14 km (~30–45 min)",
            distanceFromClub: "5.17 km (~12–15 min)",
            rating: "4.4",
            category: "business",
            image: "assets/hotels/Crowne Plaza Ahmedabad City Centre jpg.webp",
            features: ["Free WiFi", "Swimming Pool", "Gym", "Restaurant", "Conference Rooms"],
            bookingUrl: "https://www.ihg.com/crowneplaza/hotels/us/en/ahmedabad/amdch/hoteldetail?cm_mmc=GoogleMaps-_-CP-_-IN-_-AMDCH",
            distanceValue: 5.17
        },
        {
            id: 3,
            name: "Taj Skyline Ahmedabad",
            location: "Sankalp Square, Ahmedabad",
            distanceFromAirport: "17–18 km (~35–40 min)",
            distanceFromClub: "5–6 km (~12 min)",
            rating: "4.8",
            category: "luxury",
            image: "assets/hotels/Taj Skyline Ahmedabad.webp",
            features: ["Free WiFi", "Swimming Pool", "Gym", "Restaurant", "Business Center"],
            bookingUrl: "https://www.tajhotels.com/en-in/hotels/taj-skyline-ahmedabad?utm_source=Google&utm_medium=Local&utm_campaign=Taj-Skyline-Ahmedabad",
            distanceValue: 5.5
        },
        {
            id: 4,
            name: "Novotel Ahmedabad",
            location: "Sindhu Bhavan Road, Ahmedabad",
            distanceFromAirport: "18 km (~35–45 min)",
            distanceFromClub: "5.75 km (~15 min)",
            rating: "4.6",
            category: "business",
            image: "assets/hotels/Novotel Ahmedabad.webp",
            features: ["Free WiFi", "Swimming Pool", "Gym", "Restaurant", "Business Center"],
            bookingUrl: "https://all.accor.com/hotel/8173/index.en.shtml?utm_campaign=seo+maps&utm_medium=seo+maps&utm_source=google+Maps",
            distanceValue: 5.75
        },
        {
            id: 5,
            name: "Ramada by Wyndham",
            location: "Sindhu Bhavan Road, Ahmedabad",
            distanceFromAirport: "8–10 km (~20–30 min)",
            distanceFromClub: "5.18 km (~12 min)",
            rating: "4.0",
            category: "comfort",
            image: "assets/hotels/Ramada by Wyndham (Planet Landmark).webp",
            features: ["Free WiFi", "Restaurant", "Gym", "Business Center"],
            bookingUrl: "https://www.wyndhamhotels.com/ramada/ahmedabad-india/ramada-ahmedabad/rooms-rates?hotel_id=39010&checkin_date=07/19/2025&checkout_date=07/20/2025&adults=2&children=0&rooms=1&brand_id=RA&iata=00094566&cid=ME:xqah3ehakx2jwzo:39010&gclid=CjwKCAjw1dLDBhBoEiwAQNRiQXniUkhahVDFpR7ZyTXRyJvusHFADYH8EzDfFmswzHeAkV202beYPRoC0PEQAvD_BwE&adobe=srch_HPA_WYNDHAM-81220_localuniversal_1_IN_desktop_2025-07-19_default_19691981187__standard&dsclid=71386290239836160",
            distanceValue: 5.18
        },
        {
            id: 6,
            name: "Ginger – SBR / Satellite",
            location: "Satellite, Ahmedabad",
            distanceFromAirport: "15–20 km (~30–40 min)",
            distanceFromClub: "6–7 km (~15 min)",
            rating: "4.1",
            category: "comfort",
            image: "assets/hotels/Ginger – SBR  Satellite.webp",
            features: ["Free WiFi", "Restaurant", "Gym", "Meeting Rooms"],
            bookingUrl: "https://www.gingerhotels.com/destinations/hotels-in-ahmedabad",
            distanceValue: 6.5
        },
        {
            id: 7,
            name: "Pride Plaza Hotel",
            location: "Khanpur, Ahmedabad",
            distanceFromAirport: "16–18 km (~35 min)",
            distanceFromClub: "7.09 km (~15–18 min)",
            rating: "4.2",
            category: "comfort",
            image: "assets/hotels/Pride Plaza Hotel.webp",
            features: ["Free WiFi", "Restaurant", "Gym", "Conference Hall"],
            bookingUrl: "https://www.pridehotel.com/pride-plaza-hotel-ahmedabad/",
            distanceValue: 7.09
        },
        {
            id: 8,
            name: "ITC Narmada",
            location: "Ashram Road, Ahmedabad",
            distanceFromAirport: "12–15 km (~30–40 min)",
            distanceFromClub: "8.15 km (~20 min)",
            rating: "5.0",
            category: "luxury",
            image: "assets/hotels/ITC Narmada.webp",
            features: ["Free WiFi", "Swimming Pool", "Spa", "Restaurant", "Conference Room"],
            bookingUrl: "https://www.itchotels.com/in/en/itcnarmada-ahmedabad",
            distanceValue: 8.15
        },
        {
            id: 9,
            name: "Hyatt Vastrapur",
            location: "Vastrapur, Ahmedabad",
            distanceFromAirport: "15–18 km (~35 min)",
            distanceFromClub: "12.55 km (~25 min)",
            rating: "4.9",
            category: "luxury",
            image: "assets/hotels/Hyatt Vastrapur.webp",
            features: ["Free WiFi", "Swimming Pool", "Spa", "Multiple Restaurants", "Gym"],
            bookingUrl: "https://www.hyatt.com/en-US/hotel/india/hyatt-ahmedabad/amdhy",
            distanceValue: 12.55
        },
        {
            id: 10,
            name: "Renaissance Ahmedabad",
            location: "Sindhu Bhavan Road, Ahmedabad",
            distanceFromAirport: "16–18 km (~35 min)",
            distanceFromClub: "10.63 km (~20 min)",
            rating: "4.7",
            category: "luxury",
            image: "assets/hotels/Renaissance Ahmedabad.webp",
            features: ["Free WiFi", "Swimming Pool", "Spa", "Restaurant", "Conference Hall"],
            bookingUrl: "https://www.marriott.com/en-us/hotels/amdbr-renaissance-ahmedabad-hotel/overview/",
            distanceValue: 10.63
        },
        {
            id: 11,
            name: "Courtyard by Marriott Sindhu Bhavan",
            location: "Sindhu Bhavan Road, Ahmedabad",
            distanceFromAirport: "17 km (~35–40 min)",
            distanceFromClub: "9–11 km (~20 min)",
            rating: "4.5",
            category: "business",
            image: "assets/hotels/Courtyard by Marriott Sindhu Bhavan.webp",
            features: ["Free WiFi", "Swimming Pool", "Gym", "Restaurant", "Business Center"],
            bookingUrl: "https://www.marriott.com/en-us/hotels/amdsb-courtyard-ahmedabad-sindhu-bhavan-road/overview/",
            distanceValue: 10
        },
        {
            id: 12,
            name: "Courtyard by Marriott Ramdevnagar",
            location: "Ramdevnagar, Ahmedabad",
            distanceFromAirport: "17 km (~35–40 min)",
            distanceFromClub: "9–11 km (~20 min)",
            rating: "4.5",
            category: "business",
            image: "assets/hotels/Courtyard by Marriott Ramdevnagar.webp",
            features: ["Free WiFi", "Swimming Pool", "Gym", "Restaurant", "Meeting Rooms"],
            bookingUrl: "https://www.marriott.com/en-us/hotels/amdcy-courtyard-ahmedabad/overview/",
            distanceValue: 10
        },
        {
            id: 13,
            name: "Taj Vivanta",
            location: "SG Highway, Ahmedabad",
            distanceFromAirport: "17 km (~35–40 min)",
            distanceFromClub: "5–6 km (~12 min)",
            rating: "4.8",
            category: "luxury",
            image: "assets/hotels/Taj Vivanta.webp",
            features: ["Free WiFi", "Swimming Pool", "Spa", "Fine Dining", "Gym"],
            bookingUrl: "https://www.vivantahotels.com/en-in/hotels/vivanta-ahmedabad",
            distanceValue: 5.5
        }
    ];

    // ========================================
    // State Management
    // ========================================

    let currentFilters = {
        category: 'all',
        distance: 'all',
        sort: 'distance'
    };

    let filteredHotels = [...hotelsData];

    // ========================================
    // DOM Elements
    // ========================================

    const elements = {
        categoryFilter: document.getElementById('categoryFilter'),
        distanceFilter: document.getElementById('distanceFilter'),
        sortSelect: document.getElementById('sortSelect'),
        resetButton: document.getElementById('resetFilters'),
        hotelsGrid: document.getElementById('hotelsGrid'),
        hotelsCount: document.getElementById('hotelsCount'),
        loadingState: document.getElementById('loadingState'),
        emptyState: document.getElementById('emptyState')
    };

    // ========================================
    // Hotel Card Creation
    // ========================================

    function createHotelCard(hotel) {
        return `
            <div class="hotel-card" data-hotel-id="${hotel.id}" data-category="${hotel.category}" data-distance="${hotel.distanceValue}">
                <div class="hotel-image">
                    <img src="${hotel.image}" alt="${hotel.name}" loading="lazy" 
                         onerror="this.src='assets/icons/hotel-placeholder.png'">
                    <div class="hotel-rating">
                        <i class="fas fa-star"></i> ${hotel.rating}
                    </div>
                    <div class="hotel-distance">
                        <i class="fas fa-route"></i> ${hotel.distanceFromClub}
                    </div>
                </div>
                <div class="hotel-content">
                    <h3 class="hotel-name">${hotel.name}</h3>
                    <div class="hotel-location">
                        <i class="fas fa-map-marker-alt"></i>
                        ${hotel.location}
                    </div>
                    <div class="hotel-distances">
                        <div class="distance-info">
                            <i class="fas fa-plane"></i>
                            <span>Airport: ${hotel.distanceFromAirport}</span>
                        </div>
                        <div class="distance-info">
                            <i class="fas fa-building"></i>
                            <span>Club O7: ${hotel.distanceFromClub}</span>
                        </div>
                    </div>
                    <div class="hotel-features">
                        ${hotel.features.map(feature => `<span class="feature-tag">${feature}</span>`).join('')}
                    </div>
                    <div class="hotel-actions">
                        <a href="${hotel.bookingUrl}" class="book-now-btn" target="_blank" rel="noopener">
                            <i class="fas fa-calendar-check"></i>
                            Book Now
                        </a>
                    </div>
                </div>
            </div>
        `;
    }

    // ========================================
    // Filtering Functions
    // ========================================

    function applyFilters() {
        let filtered = [...hotelsData];

        // Category filter
        if (currentFilters.category !== 'all') {
            filtered = filtered.filter(hotel => hotel.category === currentFilters.category);
        }

        // Distance filter
        if (currentFilters.distance !== 'all') {
            switch (currentFilters.distance) {
                case 'near':
                    filtered = filtered.filter(hotel => hotel.distanceValue <= 10);
                    break;
                case 'medium':
                    filtered = filtered.filter(hotel => hotel.distanceValue > 10 && hotel.distanceValue <= 15);
                    break;
                case 'far':
                    filtered = filtered.filter(hotel => hotel.distanceValue > 15);
                    break;
            }
        }

        // Sort results
        switch (currentFilters.sort) {
            case 'distance':
                filtered.sort((a, b) => a.distanceValue - b.distanceValue);
                break;
            case 'rating':
                filtered.sort((a, b) => parseFloat(b.rating) - parseFloat(a.rating));
                break;
            case 'name':
                filtered.sort((a, b) => a.name.localeCompare(b.name));
                break;
        }

        filteredHotels = filtered;
        updateHotelsDisplay();
    }

    // ========================================
    // Display Functions
    // ========================================

    function updateHotelsDisplay() {
        if (!elements.hotelsGrid) return;

        // Update count
        if (elements.hotelsCount) {
            elements.hotelsCount.textContent = filteredHotels.length;
        }

        // Show loading state
        showLoadingState();

        // Simulate loading delay for better UX
        setTimeout(() => {
            if (filteredHotels.length === 0) {
                showEmptyState();
            } else {
                displayHotels();
            }
        }, 500);
    }

    function displayHotels() {
        const hotelsHTML = filteredHotels.map(hotel => createHotelCard(hotel)).join('');
        elements.hotelsGrid.innerHTML = hotelsHTML;
        
        hideLoadingState();
        hideEmptyState();
        
        // Add entrance animation
        const cards = elements.hotelsGrid.querySelectorAll('.hotel-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            setTimeout(() => {
                card.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    function showLoadingState() {
        if (elements.loadingState) {
            elements.loadingState.style.display = 'block';
        }
        if (elements.hotelsGrid) {
            elements.hotelsGrid.style.display = 'none';
        }
        hideEmptyState();
    }

    function hideLoadingState() {
        if (elements.loadingState) {
            elements.loadingState.style.display = 'none';
        }
        if (elements.hotelsGrid) {
            elements.hotelsGrid.style.display = 'grid';
        }
    }

    function showEmptyState() {
        hideLoadingState();
        if (elements.emptyState) {
            elements.emptyState.style.display = 'block';
        }
        if (elements.hotelsGrid) {
            elements.hotelsGrid.style.display = 'none';
        }
    }

    function hideEmptyState() {
        if (elements.emptyState) {
            elements.emptyState.style.display = 'none';
        }
    }

    // ========================================
    // Event Handlers
    // ========================================

    function handleCategoryFilter(event) {
        currentFilters.category = event.target.value;
        applyFilters();
    }

    function handleDistanceFilter(event) {
        currentFilters.distance = event.target.value;
        applyFilters();
    }

    function handleSortChange(event) {
        currentFilters.sort = event.target.value;
        applyFilters();
    }

    function handleResetFilters() {
        currentFilters = {
            category: 'all',
            distance: 'all',
            sort: 'distance'
        };

        // Reset form elements
        if (elements.categoryFilter) elements.categoryFilter.value = 'all';
        if (elements.distanceFilter) elements.distanceFilter.value = 'all';
        if (elements.sortSelect) elements.sortSelect.value = 'distance';

        applyFilters();
    }

    // ========================================
    // Global Functions (for inline onclick handlers)
    // ========================================

    window.resetFilters = handleResetFilters;

    // ========================================
    // Event Listeners
    // ========================================

    function initEventListeners() {
        // Filter event listeners
        if (elements.categoryFilter) {
            elements.categoryFilter.addEventListener('change', handleCategoryFilter);
        }

        if (elements.distanceFilter) {
            elements.distanceFilter.addEventListener('change', handleDistanceFilter);
        }

        if (elements.sortSelect) {
            elements.sortSelect.addEventListener('change', handleSortChange);
        }

        if (elements.resetButton) {
            elements.resetButton.addEventListener('click', handleResetFilters);
        }
    }

    // ========================================
    // Initialization
    // ========================================

    function init() {
        try {
            // Initialize event listeners
            initEventListeners();
            
            // Apply initial filters and display hotels
            applyFilters();
            
            console.log('Nearby Hotels page initialized successfully');
            
        } catch (error) {
            console.error('Hotels page error:', error);
        }
    }

    // ========================================
    // Page Load
    // ========================================

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})(); 