/* Contact Us Page Styles - Modern Professional Style */

/* Global heading font family consistency for contact page */
.contact-info-bar h1,
.contact-info-bar h2,
.contact-info-bar h3,
.contact-info-bar h4,
.contact-info-bar h5,
.contact-info-bar h6,
.contact-description h1,
.contact-description h2,
.contact-description h3,
.contact-description h4,
.contact-description h5,
.contact-description h6,
.contact-form-section h1,
.contact-form-section h2,
.contact-form-section h3,
.contact-form-section h4,
.contact-form-section h5,
.contact-form-section h6,
.secretariat-section h1,
.secretariat-section h2,
.secretariat-section h3,
.secretariat-section h4,
.secretariat-section h5,
.secretariat-section h6,
.map-section h1,
.map-section h2,
.map-section h3,
.map-section h4,
.map-section h5,
.map-section h6 {
    font-family: var(--font-family-primary) !important;
    font-weight: var(--font-weight-bold);
    color: var(--primary-blue-dark);
}

/* Professional font sizes for heading levels - Desktop First */
.contact-description h2,
.secretariat-section h2,
.map-section h2 {
    font-size: 4.5rem !important;
    font-weight: 800;
    line-height: 1.2;
    letter-spacing: -0.02em;
}

.contact-form-wrapper h3 {
    font-size: 3rem !important;
    font-weight: 700;
    line-height: 1.3;
}

.contact-info-sidebar h4 {
    font-size: 2.2rem !important;
    font-weight: 600;
    line-height: 1.4;
}

.contact-info-item h5 {
    font-size: 1.8rem !important;
    font-weight: 600;
    line-height: 1.4;
}

.secretariat-card h4 {
    font-size: 2rem !important;
    font-weight: 600;
    line-height: 1.4;
}

/* Ensure all body text uses primary font family */
.contact-info-bar p,
.contact-info-bar span,
.contact-info-bar label,
.contact-description p,
.contact-description span,
.contact-description label,
.contact-form-section p,
.contact-form-section span,
.contact-form-section label,
.contact-form-section input,
.contact-form-section textarea,
.contact-form-section button,
.secretariat-section p,
.secretariat-section span,
.secretariat-section label,
.secretariat-section a,
.map-section p,
.map-section span,
.map-section label,
.map-section a {
    font-family: var(--font-family-primary) !important;
}

/* Professional body text sizes - Reduced for better balance */
.contact-description p,
.secretariat-section > .container > p,
.map-section p {
    font-size: 1.7rem !important;
    line-height: 1.6;
    font-weight: 400;
}

.contact-info-item p,
.contact-form-section label,
.contact-links a,
.contact-links span {
    font-size: 1.5rem !important;
    line-height: 1.5;
    font-weight: 400;
}

.contact-form-section input,
.contact-form-section textarea {
    font-size: 1.4rem !important;
    line-height: 1.5;
    padding: 1.2rem 1.5rem;
}

.contact-form-section label {
    font-size: 1.4rem !important;
    font-weight: 600;
    margin-bottom: 0.8rem;
}

.submit-btn {
    font-size: 1.5rem !important;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.secretariat-details .position {
    font-size: 1.4rem !important;
    font-weight: 500;
}

/* Contact page specific quick info styling */
.contact-quick-info {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 3rem;
}

.quick-contact-item {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    padding: 1.5rem 2.5rem;
    border-radius: var(--radius-xl);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.quick-contact-item:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 4rem;
    height: 4rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transition: all 0.3s ease;
    flex-shrink: 0;
    aspect-ratio: 1;
}

.quick-contact-item:hover .icon-wrapper {
    transform: scale(1.1) rotate(5deg);
    background: rgba(255, 255, 255, 0.3);
}

.quick-contact-item i {
    font-size: var(--font-size-xl);
    color: white;
}

.quick-contact-item span {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    font-weight: 600;
    color: white;
    opacity: 1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    letter-spacing: 0.5px;
}

/* Contact Form Section */
.contact-form-section {
    padding: 4rem 0;
    background: var(--white);
    position: relative;
}

.contact-form-section .container {
    display: grid;
    grid-template-columns: 1.5fr 1fr;
    gap: 4rem;
    align-items: start;
    max-width: 120rem;
    margin: 0 auto;
}

.contact-form-wrapper {
    background: var(--white);
    padding: 3rem 2.5rem;
    border-radius: var(--radius-2xl);
    box-shadow: 0 10px 40px rgba(46, 89, 144, 0.1);
    position: relative;
    border: 1px solid rgba(46, 89, 144, 0.08);
    overflow: hidden;
}

.contact-info-sidebar {
    background: var(--white);
    padding: 4rem 3rem;
    border-radius: var(--radius-2xl);
    box-shadow: 0 10px 40px rgba(46, 89, 144, 0.1);
    border: 1px solid rgba(46, 89, 144, 0.08);
    height: fit-content;
}

.contact-info-sidebar h4 {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xl);
    color: var(--primary-blue-dark);
    margin-bottom: 2rem;
    font-weight: var(--font-weight-bold);
    text-align: center;
}

.contact-info-sidebar .contact-info-items {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    background: transparent;
    border-radius: 0;
    box-shadow: none;
    padding: 0;
}

.contact-form-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 8px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-orange), var(--primary-green));
    background-size: 200% 200%;
    animation: gradientFlow 3s ease infinite;
}

.contact-form-wrapper h3 {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-2xl);
    margin-bottom: 2rem;
    font-weight: var(--font-weight-bold);
    text-align: center;
    position: relative;
    padding-bottom: 1rem;
    color: #1a3a5c;
}

/* Remove underline decoration */
.contact-form-wrapper h3::after {
    display: none;
}

/* Form Styles */
.contact-form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
}

.form-group {
    position: relative;
}

.form-group label {
    font-family: var(--font-family-primary);
    display: block;
    margin-bottom: 0.8rem;
    font-weight: 600;
    color: var(--gray-700);
    font-size: var(--font-size-sm);
    transition: all 0.3s ease;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1.5rem 2rem;
    border: none;
    border-radius: var(--radius-xl);
    font-size: var(--font-size-base);
    font-family: var(--font-family-primary);
    background: var(--gray-50);
    transition: all 0.3s ease;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
    color: var(--gray-800);
    background-color: var(--gray-50);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--gray-400);
    font-weight: var(--font-weight-medium);
    opacity: 0.7;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    background: var(--white);
    box-shadow: 0 0 0 2px var(--primary-blue-light), 0 4px 12px rgba(46, 89, 144, 0.1);
    transform: translateY(-2px);
    background-color: white;
}

.form-group textarea {
    resize: vertical;
    min-height: 12rem;
}

/* Submit Button */
.submit-btn {
    font-family: var(--font-family-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 1.2rem 2.8rem;
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark));
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 220px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(46, 89, 144, 0.3);
}

.submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.6s ease;
}

.submit-btn:hover {
    background: linear-gradient(135deg, var(--primary-blue-dark), var(--primary-blue));
    transform: translateY(-3px);
    box-shadow: 0 12px 25px rgba(46, 89, 144, 0.4);
}

.submit-btn:hover::before {
    animation: shimmerButton 1.5s infinite;
}

.submit-btn i {
    font-size: var(--font-size-base);
    transition: all 0.3s ease;
}

.submit-btn:hover i {
    transform: translateX(5px);
}

@keyframes shimmerButton {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Contact Info Bar */
.contact-info-bar {
    padding: 0;
    background: var(--white);
    position: relative;
    z-index: 10;
    margin-top: -5rem;
}

.contact-info-items {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 3rem;
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    padding: 3rem;
    position: relative;
    overflow: hidden;
}

.contact-info-items::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(46, 89, 144, 0.03) 0%, rgba(255, 140, 66, 0.03) 100%);
    z-index: -1;
}

.contact-info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 2.5rem;
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
    position: relative;
    background: var(--white);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(46, 89, 144, 0.08);
}

.contact-info-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

.contact-info-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 40%;
    background: var(--primary-blue);
    border-radius: 2px 0 0 2px;
}

.contact-info-item:nth-child(2)::before {
    background: var(--primary-orange);
}

.contact-info-item:nth-child(3)::before {
    background: var(--primary-green);
}

.contact-info-item h5 {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-lg);
    color: var(--primary-blue-dark);
    margin-bottom: 1.5rem;
    font-weight: var(--font-weight-bold);
    position: relative;
}

.contact-info-item p {
    font-family: var(--font-family-primary);
    color: var(--gray-700);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
}

/* Contact Description */
.contact-description {
    padding: 3rem 0 0rem;
    text-align: center;
    background: var(--white);
}

.contact-description h2 {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-3xl);
    color: var(--primary-blue-dark);
    margin-bottom: 2rem;
    font-weight: var(--font-weight-bold);
    position: relative;
    display: inline-block;
}

/* Remove underline decoration */
.contact-description h2::after {
    display: none;
}

.contact-description p {
    font-family: var(--font-family-primary);
    color: var(--gray-700);
    font-size: var(--font-size-md);
    max-width: 70rem;
    margin: 0 auto;
    line-height: 1.8;
}

/* Secretariat Section */
.secretariat-section {
    padding: 2rem 0;
    background: linear-gradient(135deg, rgba(46, 89, 144, 0.03) 0%, rgba(255, 140, 66, 0.03) 100%);
    position: relative;
    overflow: hidden;
}

.secretariat-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%232e5990' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.5;
}

.secretariat-section h2 {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-3xl);
    color: var(--primary-blue-dark);
    margin-bottom: 2rem;
    font-weight: var(--font-weight-bold);
    text-align: center;
    position: relative;
    display: inline-block;
    width: 100%;
}

/* Remove underline decoration */
.secretariat-section h2::after {
    display: none;
}

.secretariat-section > .container > p {
    font-family: var(--font-family-primary);
    color: var(--gray-700);
    font-size: var(--font-size-md);
    text-align: center;
    margin-bottom: 5rem;
    max-width: 70rem;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.8;
}

.secretariat-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(35rem, 1fr));
    gap: 3rem;
    max-width: 120rem;
    margin: 0 auto;
}

.secretariat-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: 3rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(46, 89, 144, 0.08);
    position: relative;
    overflow: hidden;
    height: auto;
    min-height: 30rem;
    display: flex;
    flex-direction: column;
}

.secretariat-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 2.5rem;
    position: relative;
    padding-bottom: 2rem;
}

.card-header::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-orange));
    border-radius: 1px;
}

.avatar-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 5rem;
    height: 5rem;
    background: linear-gradient(135deg, var(--primary-blue-light), var(--primary-blue));
    border-radius: 50%;
    color: white;
    box-shadow: 0 8px 20px rgba(46, 89, 144, 0.3);
    transition: all 0.3s ease;
    flex-shrink: 0;
    aspect-ratio: 1;
    min-width: 5rem;
    min-height: 5rem;
}

.avatar-wrapper i {
    font-size: var(--font-size-xl);
    color: white !important;
}

.secretariat-card h4 {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-lg);
    color: var(--primary-blue-dark);
    font-weight: var(--font-weight-bold);
    margin: 0;
    line-height: 1.4;
}

.secretariat-details {
    padding: 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.secretariat-details .position {
    font-family: var(--font-family-primary);
    color: var(--primary-orange);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    margin-bottom: 2rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.contact-links {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-links a,
.contact-links span {
    font-family: var(--font-family-primary);
    display: flex;
    align-items: center;
    gap: 1.2rem;
    color: var(--gray-700);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    transition: all 0.3s ease;
}

.contact-links a:hover {
    color: var(--primary-blue);
    transform: translateX(5px);
}

.contact-links i {
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-blue);
    border-radius: 50%;
    color: white !important;
    font-size: var(--font-size-xs);
    transition: all 0.3s ease;
    flex-shrink: 0;
    aspect-ratio: 1;
    min-width: 2rem;
    min-height: 2rem;
}

.contact-links a:hover i {
    background: var(--primary-blue-dark);
    color: white !important;
    transform: scale(1.1);
}

.contact-links span i {
    background: var(--gray-500);
}

.contact-links span:hover i {
    background: var(--gray-600);
}

/* Map Section */
.map-section {
    padding: 2rem 0;
    background: var(--white);
    position: relative;
}

.map-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%232e5990' fill-opacity='0.03'%3E%3Ccircle cx='20' cy='20' r='2'/%3E%3Ccircle cx='10' cy='10' r='1'/%3E%3Ccircle cx='30' cy='10' r='1'/%3E%3Ccircle cx='10' cy='30' r='1'/%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.5;
    z-index: 0;
}

.map-section .container {
    position: relative;
    z-index: 1;
}

.map-header {
    text-align: center;
    margin-bottom: 2rem;
}

.map-header h2 {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-3xl);
    color: var(--primary-blue-dark);
    margin-bottom: 2rem;
    font-weight: var(--font-weight-bold);
    position: relative;
    display: inline-block;
}

/* Remove underline decoration */
.map-header h2::after {
    display: none;
}

.map-header p {
    font-family: var(--font-family-primary);
    color: var(--gray-700);
    font-size: var(--font-size-md);
    line-height: 1.8;
}

.map-container {
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    margin-bottom: 3rem;
    border: 1px solid rgba(46, 89, 144, 0.08);
}

.map-footer {
    text-align: center;
}

.direction-btn a {
    font-family: var(--font-family-primary);
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    padding: 1.2rem 2.5rem;
    background: linear-gradient(135deg, var(--primary-green), var(--primary-green-dark));
    color: white;
    text-decoration: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    transition: all 0.3s ease;
    box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
}

.direction-btn a:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 25px rgba(76, 175, 80, 0.4);
}

.direction-btn i {
    font-size: var(--font-size-base);
}

/* Success Message */
.success-message {
    background: linear-gradient(135deg, var(--primary-green), var(--primary-green-light));
    color: white;
    padding: 2rem;
    border-radius: var(--radius-lg);
    margin-bottom: 3rem;
    display: none;
    align-items: center;
    gap: 1.5rem;
    box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
    animation: slideIn 0.5s ease;
}

.success-message.show {
    display: flex;
}

.success-message i {
    font-size: var(--font-size-xl);
    color: white;
}

/* Submit Button Loading State */
.submit-btn.loading {
    background: var(--gray-400);
    cursor: not-allowed;
    pointer-events: none;
}

.submit-btn.loading::after {
    content: '';
    position: absolute;
    right: 2rem;
    top: 50%;
    transform: translateY(-50%);
    width: 2rem;
    height: 2rem;
    border: 2px solid transparent;
    border-top-color: white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: translateY(-50%) rotate(0deg); }
    to { transform: translateY(-50%) rotate(360deg); }
}

/* Additional Animations */
@keyframes shimmerTopBar {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes gradientFlow {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .contact-info-items {
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
        padding: 2.5rem;
    }

    .secretariat-grid {
        grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
    }
}

@media (max-width: 992px) {
    /* Medium Tablet Size - Professional Scaling */
    .contact-description h2,
    .secretariat-section h2,
    .map-header h2 {
        font-size: 3.8rem !important;
        font-weight: 700;
        line-height: 1.2;
    }

    .contact-form-wrapper h3 {
        font-size: 2.5rem !important;
        font-weight: 600;
        line-height: 1.3;
    }

    .contact-info-sidebar h4 {
        font-size: 2rem !important;
        font-weight: 600;
    }

    .contact-info-item h5 {
        font-size: 1.6rem !important;
        font-weight: 600;
    }

    .secretariat-card h4 {
        font-size: 1.8rem !important;
        font-weight: 600;
    }

    /* Medium Tablet Body Text */
    .contact-description p,
    .secretariat-section > .container > p,
    .map-header p {
        font-size: 1.5rem !important;
        line-height: 1.6;
    }

    .contact-info-item p {
        font-size: 1.4rem !important;
        line-height: 1.5;
    }

    /* Layout Adjustments */
    .contact-info-items {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }

    .contact-info-item {
        padding: 2rem 1.5rem;
    }

    .contact-form-wrapper {
        padding: 3rem;
    }

    .secretariat-grid {
        grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
    }

    .quick-contact-item {
        flex-direction: column;
        gap: 1rem;
        padding: 1.5rem 2rem;
    }

    .quick-contact-item span {
        text-align: center;
        font-size: 1.3rem;
    }

    /* Form Fields Responsive */
    .contact-form-section input,
    .contact-form-section textarea {
        font-size: 1.3rem !important;
        padding: 1.1rem 1.4rem;
    }

    .contact-form-section label {
        font-size: 1.3rem !important;
    }
}

@media (max-width: 768px) {
    /* Professional Tablet Sizes */
    .contact-description h2,
    .secretariat-section h2,
    .map-header h2 {
        font-size: 3.2rem !important;
        font-weight: 700;
        line-height: 1.2;
    }

    .contact-form-wrapper h3 {
        font-size: 2.2rem !important;
        font-weight: 600;
        line-height: 1.3;
    }

    .contact-info-sidebar h4 {
        font-size: 1.8rem !important;
        font-weight: 600;
    }

    .contact-info-item h5 {
        font-size: 1.5rem !important;
        font-weight: 600;
    }

    .secretariat-card h4 {
        font-size: 1.6rem !important;
        font-weight: 600;
    }

    /* Professional Body Text */
    .contact-description p,
    .secretariat-section > .container > p,
    .map-header p {
        font-size: 1.4rem !important;
        line-height: 1.6;
    }

    .contact-info-item p {
        font-size: 1.3rem !important;
        line-height: 1.5;
    }

    .secretariat-details .position {
        font-size: 1.2rem !important;
        font-weight: 500;
    }

    .contact-links a,
    .contact-links span {
        font-size: 1.2rem !important;
        line-height: 1.4;
    }

    /* Layout Adjustments */
    .contact-quick-info {
        flex-direction: column;
        gap: 2rem;
        align-items: center;
        margin-top: 2rem;
    }

    .quick-contact-item {
        flex-direction: row;
        min-width: 28rem;
        max-width: 35rem;
        padding: 1.2rem 2rem;
        gap: 1.2rem;
    }

    .icon-wrapper {
        width: 3.5rem;
        height: 3.5rem;
        min-width: 3.5rem;
        min-height: 3.5rem;
    }

    .quick-contact-item span {
        text-align: left;
        font-size: 1.2rem;
    }

    /* Form Fields Responsive */
    .contact-form-section input,
    .contact-form-section textarea {
        font-size: 1.3rem !important;
        padding: 1.1rem 1.4rem;
    }

    .contact-form-section label {
        font-size: 1.3rem !important;
    }

    .contact-form-section .container {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .contact-form-wrapper {
        padding: 2.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .secretariat-grid {
        grid-template-columns: 1fr;
        gap: 2.5rem;
        max-width: 50rem;
        margin: 0 auto;
    }

    .secretariat-card {
        max-width: none;
        width: 100%;
        margin: 0;
        padding: 2.5rem;
        min-height: 26rem;
    }

    .avatar-wrapper {
        width: 6rem;
        height: 6rem;
        min-width: 6rem;
        min-height: 6rem;
    }

    .submit-btn {
        min-width: auto;
        width: 100%;
    }
}

@media (max-width: 576px) {
    /* Professional Mobile Sizes */
    .contact-description h2,
    .secretariat-section h2,
    .map-header h2 {
        font-size: 2.8rem !important;
        font-weight: 700;
        line-height: 1.2;
    }

    .contact-form-wrapper h3 {
        font-size: 2rem !important;
        font-weight: 600;
        line-height: 1.3;
    }

    .contact-info-sidebar h4 {
        font-size: 1.7rem !important;
        font-weight: 600;
    }

    .contact-info-item h5 {
        font-size: 1.4rem !important;
        font-weight: 600;
    }

    .secretariat-card h4 {
        font-size: 1.5rem !important;
        font-weight: 600;
    }

    /* Professional Mobile Body Text */
    .contact-description p,
    .secretariat-section > .container > p,
    .map-header p {
        font-size: 1.3rem !important;
        line-height: 1.6;
    }

    .contact-info-item p {
        font-size: 1.2rem !important;
        line-height: 1.5;
    }

    /* Layout Adjustments */
    .quick-contact-item {
        min-width: auto;
        max-width: none;
        width: 100%;
        padding: 1rem 1.5rem;
        gap: 1rem;
    }

    .icon-wrapper {
        width: 3rem;
        height: 3rem;
        min-width: 3rem;
        min-height: 3rem;
    }

    .quick-contact-item span {
        font-size: var(--font-size-xs);
    }

    .contact-form-wrapper {
        padding: 2rem 1.5rem;
        border-radius: var(--radius-xl);
    }

    .contact-info-sidebar {
        padding: 2rem 1.5rem;
    }

    .form-group input,
    .form-group textarea {
        padding: 1.2rem 1.5rem;
        border-radius: var(--radius-lg);
        font-size: 1.2rem !important;
    }

    .form-group label {
        font-size: 1.2rem !important;
    }

    .submit-btn {
        padding: 1.2rem 2rem;
        font-size: var(--font-size-sm);
    }

    .secretariat-section {
        padding: 3rem 0;
        padding-bottom: 2rem;
    }

    .secretariat-card {
        padding: 2rem;
        max-width: 40rem;
        margin: 0 auto;
    }

    .card-header {
        gap: 1.5rem;
    }

    .avatar-wrapper {
        width: 5.5rem;
        height: 5.5rem;
        min-width: 5.5rem;
        min-height: 5.5rem;
    }

    .map-section {
        padding: 4rem 0;
    }
}

@media (max-width: 480px) {
    /* Professional Small Mobile Sizes */
    .contact-description h2,
    .secretariat-section h2,
    .map-header h2 {
        font-size: 2.4rem !important;
        font-weight: 700;
        line-height: 1.2;
    }

    .contact-form-wrapper h3 {
        font-size: 1.8rem !important;
        font-weight: 600;
        line-height: 1.3;
        margin-top: 1rem;
    }

    .contact-info-sidebar h4 {
        font-size: 1.5rem !important;
        font-weight: 600;
    }

    .contact-info-item h5 {
        font-size: 1.3rem !important;
        font-weight: 600;
    }

    .secretariat-card h4 {
        font-size: 1.4rem !important;
        font-weight: 600;
    }

    /* Professional Small Mobile Body Text */
    .contact-description p,
    .secretariat-section > .container > p,
    .map-header p {
        font-size: 1.2rem !important;
        line-height: 1.6;
    }

    .contact-info-item p {
        font-size: 1.1rem !important;
        line-height: 1.5;
    }

    .secretariat-details .position {
        font-size: 1.1rem !important;
        font-weight: 500;
    }

    .contact-links a,
    .contact-links span {
        font-size: 1.1rem !important;
        line-height: 1.4;
    }

    /* Layout Adjustments */
    .quick-contact-item {
        padding: 1rem 1.2rem;
        gap: 0.8rem;
    }

    .icon-wrapper {
        width: 2.8rem;
        height: 2.8rem;
        min-width: 2.8rem;
        min-height: 2.8rem;
    }

    .quick-contact-item span {
        font-size: var(--font-size-xs);
    }

    .secretariat-card {
        padding: 1.5rem;
    }

    .contact-form-wrapper {
        padding: 1.5rem 1rem;
    }

    .form-group input,
    .form-group textarea {
        padding: 1rem 1.2rem;
        font-size: 1.1rem !important;
    }

    .form-group label {
        font-size: 1.1rem !important;
    }

    .avatar-wrapper {
        width: 5rem;
        height: 5rem;
        min-width: 5rem;
        min-height: 5rem;
    }
}

@media (max-width: 360px) {
    /* Extra Small Mobile Screens */
    .contact-description h2,
    .secretariat-section h2,
    .map-header h2 {
        font-size: 2rem !important;
    }

    .contact-form-wrapper h3 {
        font-size: 1.6rem !important;
    }

    .contact-info-sidebar h4 {
        font-size: 1.4rem !important;
    }

    .contact-info-item h5 {
        font-size: 1.3rem !important;
    }

    .secretariat-card h4 {
        font-size: 1.2rem !important;
        text-align: center;
    }

    /* Extra Small Body Text */
    .contact-description p,
    .secretariat-section > .container > p,
    .map-header p {
        font-size: 1.1rem !important;
        line-height: 1.4;
    }

    .contact-info-item p {
        font-size: 1rem !important;
    }

    .secretariat-details .position {
        font-size: 0.9rem !important;
    }

    .contact-links a,
    .contact-links span {
        font-size: 0.9rem !important;
    }

    /* Layout Adjustments */
    .quick-contact-item {
        padding: 1rem;
        gap: 1rem;
    }

    .icon-wrapper {
        width: 3rem;
        height: 3rem;
    }

    .quick-contact-item i {
        font-size: var(--font-size-base);
    }

    .contact-info-items {
        padding: 1.5rem 1rem;
    }

    .contact-info-item {
        padding: 1rem;
    }

    .secretariat-card {
        padding: 1rem;
    }

    .card-header {
        gap: 1rem;
        flex-direction: column;
        text-align: center;
    }

    .avatar-wrapper {
        width: 5rem;
        height: 5rem;
        flex-shrink: 0;
        aspect-ratio: 1;
        min-width: 5rem;
        min-height: 5rem;
    }
}

/* Ensure all text elements have proper base styling */
.contact-info-bar *,
.contact-description *,
.contact-form-section *,
.secretariat-section *,
.map-section * {
    color: white;
}

/* Override any potential white text on white background issues */
.contact-form-section,
.contact-description,
.map-section {
    color: var(--gray-800);
}

.contact-form-section p,
.contact-description p,
.map-section p {
    color: var(--gray-700);
}