<svg class="lds-equalizer" width="93px"  height="93px"  xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid" style="background: none;"><g transform="rotate(180 50 50)"><rect ng-attr-x="{{11.11111111111111 - config.width/2}}" y="15" ng-attr-width="{{config.width}}" height="45.7913" fill="#56418B" x="6.111111111111111" width="10">
  <animate attributeName="height" calcMode="spline" values="50;75;10;50" times="0;0.33;0.66;1" ng-attr-dur="{{config.speed}}" keySplines="0.5 0 0.5 1;0.5 0 0.5 1;0.5 0 0.5 1" repeatCount="indefinite" begin="-0.325s" dur="1.3">
</animate></rect><rect ng-attr-x="{{22.22222222222222 - config.width/2}}" y="15" ng-attr-width="{{config.width}}" height="50.5588" fill="#1c94d2" x="17.22222222222222" width="10">
  <animate attributeName="height" calcMode="spline" values="50;75;10;50" times="0;0.33;0.66;1" ng-attr-dur="{{config.speed}}" keySplines="0.5 0 0.5 1;0.5 0 0.5 1;0.5 0 0.5 1" repeatCount="indefinite" begin="-0.48750000000000004s" dur="1.3">
</animate></rect><rect ng-attr-x="{{33.333333333333336 - config.width/2}}" y="15" ng-attr-width="{{config.width}}" height="62.452" fill="#ffed03" x="28.333333333333336" width="10">
  <animate attributeName="height" calcMode="spline" values="50;75;10;50" times="0;0.33;0.66;1" ng-attr-dur="{{config.speed}}" keySplines="0.5 0 0.5 1;0.5 0 0.5 1;0.5 0 0.5 1" repeatCount="indefinite" begin="-0.65s" dur="1.3">
</animate></rect><rect ng-attr-x="{{44.44444444444444 - config.width/2}}" y="15" ng-attr-width="{{config.width}}" height="74.4165" fill="#40cbb4" x="39.44444444444444" width="10">
  <animate attributeName="height" calcMode="spline" values="50;75;10;50" times="0;0.33;0.66;1" ng-attr-dur="{{config.speed}}" keySplines="0.5 0 0.5 1;0.5 0 0.5 1;0.5 0 0.5 1" repeatCount="indefinite" begin="-0.8125s" dur="1.3">
</animate></rect><rect ng-attr-x="{{55.55555555555556 - config.width/2}}" y="15" ng-attr-width="{{config.width}}" height="68.3185" fill="#4c35a9" x="50.55555555555556" width="10">
  <animate attributeName="height" calcMode="spline" values="50;75;10;50" times="0;0.33;0.66;1" ng-attr-dur="{{config.speed}}" keySplines="0.5 0 0.5 1;0.5 0 0.5 1;0.5 0 0.5 1" repeatCount="indefinite" begin="-0.9750000000000001s" dur="1.3">
</animate></rect><rect ng-attr-x="{{66.66666666666667 - config.width/2}}" y="15" ng-attr-width="{{config.width}}" height="20.549" fill="#56418B" x="61.66666666666667" width="10">
  <animate attributeName="height" calcMode="spline" values="50;75;10;50" times="0;0.33;0.66;1" ng-attr-dur="{{config.speed}}" keySplines="0.5 0 0.5 1;0.5 0 0.5 1;0.5 0 0.5 1" repeatCount="indefinite" begin="-0.1625s" dur="1.3">
</animate></rect><rect ng-attr-x="{{77.77777777777777 - config.width/2}}" y="15" ng-attr-width="{{config.width}}" height="10.0003" fill="#1c94d2" x="72.77777777777777" width="10">
  <animate attributeName="height" calcMode="spline" values="50;75;10;50" times="0;0.33;0.66;1" ng-attr-dur="{{config.speed}}" keySplines="0.5 0 0.5 1;0.5 0 0.5 1;0.5 0 0.5 1" repeatCount="indefinite" begin="0s" dur="1.3">
</animate></rect><rect ng-attr-x="{{88.88888888888889 - config.width/2}}" y="15" ng-attr-width="{{config.width}}" height="27.4149" fill="#ffed03" x="83.88888888888889" width="10">
  <animate attributeName="height" calcMode="spline" values="50;75;10;50" times="0;0.33;0.66;1" ng-attr-dur="{{config.speed}}" keySplines="0.5 0 0.5 1;0.5 0 0.5 1;0.5 0 0.5 1" repeatCount="indefinite" begin="-1.1375s" dur="1.3">
</animate></rect></g></svg>