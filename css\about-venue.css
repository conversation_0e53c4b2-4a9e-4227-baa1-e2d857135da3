/* About Venue - Clean & Minimal Design with Subtle Enhancements */

/* Main venue content section */
.venue-content {
    padding: 80px 0;
    background: #ffffff;
    position: relative;
    overflow: hidden;
}

/* Subtle background shapes */
.venue-content::before {
    content: '';
    position: absolute;
    top: -50px;
    right: -50px;
    width: 200px;
    height: 200px;
    background: linear-gradient(135deg, rgba(46, 89, 144, 0.03), rgba(255, 140, 66, 0.03));
    border-radius: 50%;
    z-index: 1;
}

.venue-content::after {
    content: '';
    position: absolute;
    bottom: -80px;
    left: -80px;
    width: 300px;
    height: 300px;
    background: linear-gradient(135deg, rgba(255, 140, 66, 0.02), rgba(46, 89, 144, 0.02));
    border-radius: 50%;
    z-index: 1;
}

/* Venue image section */
.venue-image-section {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
    z-index: 2;
}

.venue-image {
    width: 100%;
    max-width: 800px;
    height: 400px;
    object-fit: cover;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;
}

/* Add floating geometric shapes around image */
.venue-image-section::before {
    content: '';
    position: absolute;
    top: -20px;
    left: 10%;
    width: 60px;
    height: 60px;
    background: rgba(255, 140, 66, 0.1);
    transform: rotate(45deg);
    z-index: 1;
}

.venue-image-section::after {
    content: '';
    position: absolute;
    bottom: -30px;
    right: 15%;
    width: 40px;
    height: 40px;
    background: rgba(46, 89, 144, 0.1);
    border-radius: 50%;
    z-index: 1;
}

/* Venue description */
.venue-description {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 80px auto;
    position: relative;
    z-index: 2;
    background: rgba(255, 255, 255, 0.9);
    padding: 40px;
    border-radius: 16px;
    backdrop-filter: blur(10px);
}

.venue-description h2 {
    margin-bottom: 30px;
    color: var(--primary-blue, #2E5990);
}

.venue-text {
    font-size: 16px;
    line-height: 1.7;
    color: #666;
    margin-bottom: 20px;
}

/* Features section */
.venue-features {
    margin-bottom: 80px;
    position: relative;
}

/* Add subtle pattern background for features */
.venue-features::before {
    content: '';
    position: absolute;
    top: 50px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(46, 89, 144, 0.1), transparent);
    z-index: 1;
}

.features-title {
    text-align: center;
    font-size: 28px;
    color: var(--primary-blue, #2E5990);
    margin-bottom: 50px;
    font-weight: 600;
    position: relative;
    z-index: 2;
}

/* Add decorative element under title */
.features-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-orange, #FF8C42), var(--primary-blue, #2E5990));
    border-radius: 2px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.feature-item {
    text-align: center;
    padding: 30px 20px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: transform 0.3s ease;
    position: relative;
    overflow: hidden;
}

/* Add subtle geometric accent */
.feature-item::before {
    content: '';
    position: absolute;
    top: -10px;
    right: -10px;
    width: 30px;
    height: 30px;
    background: rgba(255, 140, 66, 0.1);
    border-radius: 50%;
    z-index: 1;
}

.feature-item:hover {
    transform: translateY(-5px);
}

.feature-item i {
    font-size: 32px;
    color: var(--primary-orange, #FF8C42);
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

.feature-item h4 {
    font-size: 18px;
    color: var(--primary-blue, #2E5990);
    margin-bottom: 15px;
    font-weight: 600;
    position: relative;
    z-index: 2;
}

.feature-item p {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    position: relative;
    z-index: 2;
}

/* Connectivity section */
.venue-connectivity {
    margin-bottom: 80px;
    position: relative;
    background: linear-gradient(135deg, rgba(248, 249, 250, 0.5), rgba(255, 255, 255, 0.8));
    padding: 60px 0;
    border-radius: 20px;
}

/* Add decorative dots pattern */
.venue-connectivity::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 20px;
    width: 8px;
    height: 8px;
    background: rgba(46, 89, 144, 0.2);
    border-radius: 50%;
    box-shadow: 
        20px 0 0 rgba(46, 89, 144, 0.1),
        40px 0 0 rgba(255, 140, 66, 0.1),
        0 20px 0 rgba(255, 140, 66, 0.2),
        20px 20px 0 rgba(46, 89, 144, 0.1);
}

.venue-connectivity::after {
    content: '';
    position: absolute;
    bottom: 20px;
    right: 20px;
    width: 6px;
    height: 6px;
    background: rgba(255, 140, 66, 0.2);
    border-radius: 50%;
    box-shadow: 
        -15px 0 0 rgba(255, 140, 66, 0.1),
        -30px 0 0 rgba(46, 89, 144, 0.1),
        0 -15px 0 rgba(46, 89, 144, 0.2);
}

.connectivity-title {
    text-align: center;
    font-size: 28px;
    color: var(--primary-blue, #2E5990);
    margin-bottom: 50px;
    font-weight: 600;
    position: relative;
    z-index: 2;
}

/* Add decorative line under connectivity title */
.connectivity-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-blue, #2E5990), var(--primary-orange, #FF8C42));
    border-radius: 2px;
}

.connectivity-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.connectivity-item {
    text-align: center;
    padding: 30px 20px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    transition: box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

/* Add subtle triangle accent */
.connectivity-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 0;
    border-left: 20px solid rgba(46, 89, 144, 0.1);
    border-bottom: 20px solid transparent;
    z-index: 1;
}

.connectivity-item:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.connectivity-item i {
    font-size: 32px;
    color: var(--primary-blue, #2E5990);
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

.connectivity-item h4 {
    font-size: 18px;
    color: var(--primary-blue, #2E5990);
    margin-bottom: 15px;
    font-weight: 600;
    position: relative;
    z-index: 2;
}

.connectivity-item p {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    position: relative;
    z-index: 2;
}

/* Call to action section */
.venue-cta {
    text-align: center;
    background: linear-gradient(135deg, #f8f9fa, rgba(255, 255, 255, 0.9));
    padding: 60px 40px;
    border-radius: 12px;
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
}

/* Add elegant wave pattern */
.venue-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, 
        var(--primary-blue, #2E5990) 0%, 
        var(--primary-orange, #FF8C42) 50%, 
        var(--primary-blue, #2E5990) 100%);
    z-index: 1;
}

.venue-cta::after {
    content: '';
    position: absolute;
    bottom: -30px;
    right: -30px;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255, 140, 66, 0.05), transparent);
    border-radius: 50%;
    z-index: 1;
}

.cta-title {
    font-size: 28px;
    color: var(--primary-blue, #2E5990);
    margin-bottom: 20px;
    font-weight: 600;
    position: relative;
    z-index: 2;
}

.cta-text {
    font-size: 16px;
    color: #666;
    margin-bottom: 40px;
    line-height: 1.6;
    position: relative;
    z-index: 2;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    position: relative;
    z-index: 2;
}

.btn-cta {
    display: inline-block;
    padding: 15px 30px;
    border-radius: 8px;
    text-decoration: none;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 150px;
}

.btn-cta.primary {
    background: var(--primary-orange, #FF8C42);
    color: white;
}

.btn-cta.primary:hover {
    background: #e67e22;
    transform: translateY(-2px);
}

.btn-cta.secondary {
    background: transparent;
    color: var(--primary-blue, #2E5990);
    border: 2px solid var(--primary-blue, #2E5990);
}

.btn-cta.secondary:hover {
    background: var(--primary-blue, #2E5990);
    color: white;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .venue-content {
        padding: 60px 0;
    }
    
    .venue-image-section {
        margin-bottom: 40px;
    }
    
    .venue-image {
        height: 250px;
        border-radius: 8px;
    }
    
    .venue-description {
        margin-bottom: 60px;
    }
    
    .features-title,
    .connectivity-title,
    .cta-title {
        font-size: 24px;
        margin-bottom: 30px;
    }
    
    .venue-features,
    .venue-connectivity {
        margin-bottom: 60px;
    }
    
    .features-grid,
    .connectivity-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .feature-item,
    .connectivity-item {
        padding: 25px 15px;
    }
    
    .venue-cta {
        padding: 40px 20px;
        border-radius: 8px;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }
    
    .btn-cta {
        width: 100%;
        max-width: 250px;
        padding: 12px 25px;
    }
}

@media (max-width: 480px) {
    .venue-content {
        padding: 40px 0;
    }
    
    .venue-image {
        height: 200px;
    }
    
    .venue-text {
        font-size: 15px;
    }
    
    .feature-item i,
    .connectivity-item i {
        font-size: 28px;
        margin-bottom: 15px;
    }
    
    .feature-item h4,
    .connectivity-item h4 {
        font-size: 16px;
        margin-bottom: 10px;
    }
    
    .feature-item p,
    .connectivity-item p {
        font-size: 13px;
    }
}