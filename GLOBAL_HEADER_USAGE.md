# Global Page Hero Component Usage Guide

## Overview
The Global Page Hero component provides a consistent, beautiful header section that can be used across all pages of the website. It includes responsive design, animations, and customizable content.

## Basic Usage

### Step 1: Add the HTML Structure
```html
<!-- Global Page Hero Section -->
<section class="global-page-hero">
    <div class="container">
        <div class="global-hero-content">
            <h1 class="global-hero-title">Your Page Title</h1>
            <p class="global-hero-subtitle">Your page subtitle or description</p>
            <div class="global-hero-badge">
                <i class="fas fa-your-icon"></i>
                <span>Your badge text</span>
            </div>
            
            <!-- Optional Quick Info Section -->
            <div class="global-hero-quick-info">
                <div class="global-quick-info-item">
                    <div class="global-quick-info-icon">
                        <i class="fas fa-icon-1"></i>
                    </div>
                    <div class="global-quick-info-text">Quick Info 1</div>
                </div>
                <div class="global-quick-info-item">
                    <div class="global-quick-info-icon">
                        <i class="fas fa-icon-2"></i>
                    </div>
                    <div class="global-quick-info-text">Quick Info 2</div>
                </div>
                <!-- Add more items as needed -->
            </div>
        </div>
    </div>
</section>
```

### Step 2: That's It!
The CSS is already included in your main stylesheets. Just add the HTML and you're done!

## Customization Options

### 1. Change Title and Subtitle
```html
<h1 class="global-hero-title">About Us</h1>
<p class="global-hero-subtitle">Learn More About Our Organization</p>
```

### 2. Customize Badge
```html
<div class="global-hero-badge">
    <i class="fas fa-info-circle"></i>
    <span>Learn More</span>
</div>
```

### 3. Add Different Quick Info Items
```html
<div class="global-quick-info-item">
    <div class="global-quick-info-icon">
        <i class="fas fa-clock"></i>
    </div>
    <div class="global-quick-info-text">24/7 Support</div>
</div>
```

### 4. Remove Quick Info Section (Optional)
If you don't need the quick info, just remove the entire `global-hero-quick-info` div:
```html
<!-- Remove this entire section if not needed -->
<div class="global-hero-quick-info">
    <!-- ... -->
</div>
```

## Examples

### About Page Example
```html
<section class="global-page-hero">
    <div class="container">
        <div class="global-hero-content">
            <h1 class="global-hero-title">About Us</h1>
            <p class="global-hero-subtitle">Discover Our Mission and Vision</p>
            <div class="global-hero-badge">
                <i class="fas fa-heart"></i>
                <span>Serving Since 1995</span>
            </div>
        </div>
    </div>
</section>
```

### Services Page Example
```html
<section class="global-page-hero">
    <div class="container">
        <div class="global-hero-content">
            <h1 class="global-hero-title">Our Services</h1>
            <p class="global-hero-subtitle">Professional Medical Solutions</p>
            <div class="global-hero-badge">
                <i class="fas fa-medical-kit"></i>
                <span>Expert Care</span>
            </div>
            
            <div class="global-hero-quick-info">
                <div class="global-quick-info-item">
                    <div class="global-quick-info-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="global-quick-info-text">24/7 Available</div>
                </div>
                <div class="global-quick-info-item">
                    <div class="global-quick-info-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="global-quick-info-text">Quality Assured</div>
                </div>
            </div>
        </div>
    </div>
</section>
```

## Features

### ✅ Fully Responsive
- Works perfectly on all devices (mobile, tablet, desktop)
- Device-specific optimizations for common phones and tablets
- Adaptive spacing and font sizes

### ✅ Beautiful Animations
- Gradient text animation on title
- Pulsing badge icon animation
- Smooth hover effects on quick info items
- Animated background gradients

### ✅ Consistent Styling
- Matches your website's color scheme
- Uses your existing font variables
- Consistent spacing and proportions

### ✅ Easy to Implement
- Just add one class: `global-page-hero`
- No additional CSS needed
- Works with any content

## Browser Support
- ✅ Chrome/Edge/Safari (Modern browsers)
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)
- ✅ All responsive breakpoints covered

## Need Help?
If you need to customize further or add new variations, just let me know! 