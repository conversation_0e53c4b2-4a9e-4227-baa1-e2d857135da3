/**
 * Hero Section Manual Slider
 * Simple and reliable slider implementation
 */

// Global variables for slider
let currentSlideIndex = 0;
let slides = [];
let totalSlides = 0;
let autoSlideInterval;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for all elements to be ready
    setTimeout(initHeroSlider, 500);
});

// Initialize slider function
function initHeroSlider() {
    slides = document.querySelectorAll('.hero-slide');
    totalSlides = slides.length;
    
    console.log('Initializing slider with', totalSlides, 'slides');
    
    if (totalSlides === 0) {
        console.error('No slides found!');
        return;
    }
    
    // Show first slide
    showSlide(0);
    
    // Start auto slide if more than one slide (slower speed)
    if (totalSlides > 1) {
        startAutoSlide();
        
        // Add hover events
        const heroSection = document.querySelector('.hero-section');
        if (heroSection) {
            heroSection.addEventListener('mouseenter', stopAutoSlide);
            heroSection.addEventListener('mouseleave', startAutoSlide);
        }
    }
    
    // Add event listeners to buttons
    addButtonEventListeners();
    
    console.log('Slider initialization complete');
}

// Show specific slide
function showSlide(index) {
    console.log('Showing slide:', index);
    
    // Remove active class from all slides
    slides.forEach((slide, i) => {
        slide.classList.remove('active');
    });
    
    // Add active class to current slide
    if (slides[index]) {
        slides[index].classList.add('active');
        currentSlideIndex = index;
        console.log('Active slide is now:', index);
    }
}

// Next slide
function nextSlide() {
    if (totalSlides <= 1) return;
    
    const nextIndex = (currentSlideIndex + 1) % totalSlides;
    console.log('Auto next slide: from', currentSlideIndex, 'to', nextIndex);
    showSlide(nextIndex);
}

// Previous slide
function prevSlide() {
    if (totalSlides <= 1) return;
    
    const prevIndex = (currentSlideIndex - 1 + totalSlides) % totalSlides;
    console.log('Auto previous slide: from', currentSlideIndex, 'to', prevIndex);
    showSlide(prevIndex);
}

// Global function for buttons (onclick)
function changeSlide(direction) {
    console.log('MANUAL changeSlide called with direction:', direction);
    
    if (totalSlides <= 1) {
        console.log('Only one slide, ignoring');
        return;
    }
    
    // Stop auto slide temporarily
    stopAutoSlide();
    
    if (direction > 0) {
        const nextIndex = (currentSlideIndex + 1) % totalSlides;
        console.log('MANUAL next slide: from', currentSlideIndex, 'to', nextIndex);
        showSlide(nextIndex);
    } else {
        const prevIndex = (currentSlideIndex - 1 + totalSlides) % totalSlides;
        console.log('MANUAL previous slide: from', currentSlideIndex, 'to', prevIndex);
        showSlide(prevIndex);
    }
    
    // Restart auto slide after 3 seconds
    setTimeout(startAutoSlide, 3000);
}

// Auto slide functions - SLOWER SPEED
function startAutoSlide() {
    // Clear any existing interval first
    stopAutoSlide();
    
    if (totalSlides > 1) {
        console.log('Starting auto slide (8 seconds interval)');
        autoSlideInterval = setInterval(nextSlide, 8000); // 8 seconds instead of 5
    }
}

function stopAutoSlide() {
    if (autoSlideInterval) {
        console.log('Stopping auto slide');
        clearInterval(autoSlideInterval);
        autoSlideInterval = null;
    }
}

// Add event listeners to buttons
function addButtonEventListeners() {
    const prevBtn = document.querySelector('.prev-btn');
    const nextBtn = document.querySelector('.next-btn');
    
    console.log('Previous button found:', !!prevBtn);
    console.log('Next button found:', !!nextBtn);
    
    if (prevBtn) {
        console.log('Adding event listener to previous button');
        prevBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Previous button CLICKED!');
            changeSlide(-1);
        });
        
        // Also add mousedown for better responsiveness
        prevBtn.addEventListener('mousedown', function(e) {
            e.preventDefault();
            console.log('Previous button mouse down');
        });
        
        // Add keyboard support
        prevBtn.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                console.log('Previous button keyboard activated');
                changeSlide(-1);
            }
        });
    }
    
    if (nextBtn) {
        console.log('Adding event listener to next button');
        nextBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Next button CLICKED!');
            changeSlide(1);
        });
        
        // Also add mousedown for better responsiveness
        nextBtn.addEventListener('mousedown', function(e) {
            e.preventDefault();
            console.log('Next button mouse down');
        });
        
        // Add keyboard support
        nextBtn.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                console.log('Next button keyboard activated');
                changeSlide(1);
            }
        });
    }
    
    // Add arrow key support for entire page
    document.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowLeft') {
            console.log('Left arrow key pressed');
            changeSlide(-1);
        } else if (e.key === 'ArrowRight') {
            console.log('Right arrow key pressed');
            changeSlide(1);
        }
    });
}

// Make changeSlide globally available (for onclick in HTML)
window.changeSlide = changeSlide;

// Countdown Timer (if elements exist)
function initCountdownTimer() {
    const targetDate = new Date('December 27, 2025 00:00:00').getTime();
    
    const daysElement = document.getElementById('days');
    const hoursElement = document.getElementById('hours');
    const minutesElement = document.getElementById('minutes');
    const secondsElement = document.getElementById('seconds');
    
    if (!daysElement || !hoursElement || !minutesElement || !secondsElement) {
        return;
    }
    
    function updateCountdown() {
        const currentTime = new Date().getTime();
        const timeDifference = targetDate - currentTime;
        
        if (timeDifference < 0) {
            daysElement.textContent = '00';
            hoursElement.textContent = '00';
            minutesElement.textContent = '00';
            secondsElement.textContent = '00';
            return;
        }
        
        const days = Math.floor(timeDifference / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeDifference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeDifference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeDifference % (1000 * 60)) / 1000);
        
        daysElement.textContent = String(days).padStart(2, '0');
        hoursElement.textContent = String(hours).padStart(2, '0');
        minutesElement.textContent = String(minutes).padStart(2, '0');
        secondsElement.textContent = String(seconds).padStart(2, '0');
    }
    
    updateCountdown();
    setInterval(updateCountdown, 1000);
}

// Initialize countdown when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initCountdownTimer, 1000);
});

// Cleanup
window.addEventListener('beforeunload', function() {
    stopAutoSlide();
}); 