/* About Conference Page Styles */

/* Main About Section */
.main-about-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
}

.main-about-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/assets/icons/pattern-1.jpg') repeat;
    opacity: 0.03;
    z-index: 1;
}

.section-content {
    position: relative;
    z-index: 2;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #0B4C7A 0%, #1e88e5 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    color: white;
    font-size: 32px;
    box-shadow: 0 10px 30px rgba(11, 76, 122, 0.2);
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.section-title {
    font-size: 42px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 16px;
    line-height: 1.2;
}

.content-blocks {
    max-width: 900px;
    margin: 0 auto;
}

.content-block {
    margin-bottom: 32px;
    padding: 0 20px;
}

.lead-text {
    font-size: 20px;
    line-height: 1.7;
    color: #34495e;
    font-weight: 500;
    margin-bottom: 24px;
}

.content-block p {
    font-size: 16px;
    line-height: 1.8;
    color: #5a6c7d;
    margin-bottom: 20px;
}

.highlight-block {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #0B4C7A;
    margin-top: 40px;
}

.highlight-block p {
    color: #2c3e50;
    font-weight: 500;
}

.final-note {
    font-size: 18px;
    font-weight: 600;
    color: #0B4C7A;
    margin-top: 24px;
}

/* Conference Highlights Section */
.conference-highlights {
    padding: 80px 0;
    background: #ffffff;
}

.highlights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 32px;
    margin-top: 40px;
}

.highlight-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    padding: 32px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(11, 76, 122, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.highlight-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #0B4C7A, #1e88e5, #42a5f5);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.highlight-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.highlight-card:hover::before {
    opacity: 1;
}

.card-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
}

.card-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #0B4C7A 0%, #1e88e5 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    margin-right: 20px;
    flex-shrink: 0;
}

.card-title {
    font-size: 22px;
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.3;
    margin: 0;
}

.card-content {
    color: #5a6c7d;
    line-height: 1.7;
}

.card-content p {
    margin-bottom: 16px;
    font-size: 15px;
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.feature-list li {
    position: relative;
    padding-left: 24px;
    margin-bottom: 16px;
    font-size: 15px;
    line-height: 1.6;
}

.feature-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #0B4C7A;
    font-weight: 600;
    font-size: 16px;
}

/* Critical Issues Section */
.critical-issues-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
}

.critical-issues-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/assets/icons/pattern-2.png') repeat;
    opacity: 0.03;
    z-index: 1;
}

.section-header.centered {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
    z-index: 2;
}

.section-subtitle {
    font-size: 18px;
    color: #5a6c7d;
    margin-top: 16px;
    font-weight: 400;
}

.issues-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    margin-top: 40px;
    position: relative;
    z-index: 2;
}

.issue-item {
    background: white;
    padding: 32px 24px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(11, 76, 122, 0.1);
}

.issue-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.issue-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #0B4C7A 0%, #1e88e5 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    color: white;
    font-size: 20px;
}

.issue-item h4 {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.4;
    margin: 0;
}

/* Call to Action Section */
.cta-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #0B4C7A 0%, #1e88e5 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.cta-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 2;
}

.cta-text {
    flex: 1;
    margin-right: 40px;
}

.cta-title {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 16px;
    line-height: 1.2;
}

.cta-description {
    font-size: 18px;
    opacity: 0.9;
    line-height: 1.6;
    margin: 0;
}

.cta-actions {
    display: flex;
    gap: 16px;
    flex-shrink: 0;
}

.cta-btn {
    padding: 16px 32px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

.cta-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.cta-btn:hover::before {
    left: 100%;
}

.cta-btn.primary {
    background: white;
    color: #0B4C7A;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.cta-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
}

.cta-btn.secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.cta-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .highlights-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 24px;
    }
    
    .section-title {
        font-size: 36px;
    }
    
    .cta-content {
        flex-direction: column;
        text-align: center;
    }
    
    .cta-text {
        margin-right: 0;
        margin-bottom: 32px;
    }
}

@media (max-width: 768px) {
    .main-about-section,
    .conference-highlights,
    .critical-issues-section,
    .cta-section {
        padding: 60px 0;
    }
    
    .section-title {
        font-size: 32px;
    }
    
    .lead-text {
        font-size: 18px;
    }
    
    .highlights-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .highlight-card {
        padding: 24px;
    }
    
    .card-header {
        flex-direction: column;
        text-align: center;
    }
    
    .card-icon {
        margin-right: 0;
        margin-bottom: 16px;
    }
    
    .card-title {
        font-size: 20px;
    }
    
    .issues-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }
    
    .cta-title {
        font-size: 28px;
    }
    
    .cta-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .cta-btn {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .main-about-section,
    .conference-highlights,
    .critical-issues-section,
    .cta-section {
        padding: 40px 0;
    }
    
    .section-title {
        font-size: 28px;
    }
    
    .lead-text {
        font-size: 16px;
    }
    
    .highlight-card {
        padding: 20px;
    }
    
    .card-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .card-title {
        font-size: 18px;
    }
    
    .issues-grid {
        grid-template-columns: 1fr;
    }
    
    .issue-item {
        padding: 24px 20px;
    }
    
    .content-block {
        padding: 0 10px;
    }
    
    .highlight-block {
        padding: 24px;
    }
    
    .cta-title {
        font-size: 24px;
    }
    
    .cta-description {
        font-size: 16px;
    }
} 