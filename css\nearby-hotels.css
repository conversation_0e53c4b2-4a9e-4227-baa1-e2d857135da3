/* =============================================
   IMA NATCON 2025 - Nearby Hotels Page
   Clean & Consistent with Theme Structure
   ============================================= */

/* Force global hero title consistency - EXACT MATCH */
.global-hero-title {
    font-size: 4.5rem !important;
    font-weight: 800 !important;
    font-family: var(--font-family-primary) !important;
    line-height: 1.1 !important;
    letter-spacing: 2px !important;
    text-transform: uppercase !important;
    background: linear-gradient(135deg, #FF8C42 0%, #FFB84D 50%, #FF8C42 100%) !important;
    background-size: 200% 200% !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    text-align: center !important;
    margin-bottom: 2rem !important;
}

/* Global Hero Subtitle - Improved font size and styling */
.global-hero-subtitle {
    font-family: var(--font-family-primary) !important;
    font-size: 2rem !important;
    font-weight: 600 !important;
    color: rgba(255, 255, 255, 0.9) !important;
    margin-bottom: 3rem !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    letter-spacing: 1px !important;
    line-height: 1.4 !important;
    max-width: 70rem !important;
    margin: 0 auto 3rem !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    hyphens: auto !important;
    text-align: center !important;
    padding: 0 1rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 1rem !important;
}

.global-hero-subtitle i {
    color: #FF8C42 !important;
    font-size: 1.8rem !important;
}

/* Responsive overrides to match other pages exactly */
@media (max-width: 1200px) {
    .global-hero-title {
        font-size: 4rem !important;
        letter-spacing: 1.5px !important;
    }
    
    .global-hero-subtitle {
        font-size: 1.8rem !important;
    }
}

@media (max-width: 1024px) {
    .global-hero-title {
        font-size: 3.5rem !important;
        letter-spacing: 1.2px !important;
    }
    
    .global-hero-subtitle {
        font-size: 1.7rem !important;
        margin-bottom: 2.5rem !important;
    }
}

@media (max-width: 768px) {
    .global-hero-title {
        font-size: 2.8rem !important;
        letter-spacing: 1px !important;
    }
    
    .global-hero-subtitle {
        font-size: 1.6rem !important;
        margin-bottom: 2rem !important;
    }
}

@media (max-width: 480px) {
    .global-hero-title {
        font-size: 2.2rem !important;
        letter-spacing: 0.8px !important;
    }
    
    .global-hero-subtitle {
        font-size: 1.4rem !important;
        margin-bottom: 1.5rem !important;
    }
    
    .global-hero-subtitle i {
        font-size: 1.3rem !important;
    }
}

@media (max-width: 360px) {
    .global-hero-title {
        font-size: 1.8rem !important;
        letter-spacing: 0.5px !important;
    }
    
    .global-hero-subtitle {
        font-size: 1.2rem !important;
        margin-bottom: 1.2rem !important;
    }
    
    .global-hero-subtitle i {
        font-size: 1.1rem !important;
    }
}

/* =============================================
   Filters Section - Clean & Minimal
   ============================================= */

.filters-section {
    background: #f8fafc;
    padding: 3rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.filters-wrapper {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid #e2e8f0;
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f5f9;
}

.filters-title {
    color: #0B4C7A;
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.filters-title i {
    color: #FF8C42;
}

.results-info {
    color: #64748b;
    font-size: 1.2rem;
}

.results-count {
    font-weight: 700;
    color: #0B4C7A;
    font-size: 1.3rem;
}

.filter-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.filter-label {
    font-weight: 600;
    color: #374151;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-label i {
    color: #6b7280;
    font-size: 0.85rem;
}

.filter-select {
    padding: 0.875rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    background: white;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.filter-select:focus {
    outline: none;
    border-color: #0B4C7A;
    box-shadow: 0 0 0 3px rgba(11, 76, 122, 0.1);
}

.filter-select:hover {
    border-color: #FF8C42;
}

.reset-btn {
    padding: 0.875rem 1.5rem;
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
}

.reset-btn:hover {
    background: linear-gradient(135deg, #4b5563, #374151);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* =============================================
   Hotels Section - Clean & Compact
   ============================================= */

.hotels-section {
    padding: 0rem 0;
    background: white;
}

.hotels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.hotel-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    position: relative;
}

.hotel-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-color: #0B4C7A;
}

.hotel-image {
    height: 220px;
    background: #f3f4f6;
    position: relative;
    overflow: hidden;
}

.hotel-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.hotel-card:hover .hotel-image img {
    transform: scale(1.05);
}

.hotel-rating {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.95);
    padding: 0.5rem 0.85rem;
    border-radius: 20px;
    font-weight: 700;
    font-size: 1rem;
    color: #0B4C7A;
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.hotel-distance {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: rgba(255, 140, 66, 0.95);
    color: white;
    padding: 0.5rem 0.85rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 1rem;
    backdrop-filter: blur(8px);
}

.hotel-content {
    padding: 2rem;
}

.hotel-name {
    font-size: 1.6rem;
    font-weight: 700;
    color: #0B4C7A;
    margin-bottom: 0.75rem;
    line-height: 1.3;
}

.hotel-location {
    color: #6b7280;
    font-size: 1.1rem;
    margin-bottom: 1.2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.hotel-location i {
    color: #FF8C42;
    font-size: 1rem;
}

.hotel-distances {
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.distance-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6b7280;
    font-size: 1.05rem;
}

.distance-info i {
    color: #0B4C7A;
    font-size: 1rem;
    width: 18px;
}

.hotel-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.feature-tag {
    background: #f1f5f9;
    color: #475569;
    padding: 0.35rem 0.85rem;
    border-radius: 20px;
    font-size: 1rem;
    font-weight: 500;
}

.hotel-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.book-now-btn {
    width: 100%;
    padding: 1rem 1.75rem;
    background: linear-gradient(135deg, #FF8C42, #ff7029);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 1.15rem;
}

.book-now-btn:hover {
    background: linear-gradient(135deg, #ff7029, #e65100);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 140, 66, 0.4);
    text-decoration: none;
    color: white;
}

/* =============================================
   Loading and Empty States
   ============================================= */

.loading-state, .empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6b7280;
}

.loading-spinner i {
    font-size: 2rem;
    color: #0B4C7A;
    margin-bottom: 1rem;
}

.loading-state p {
    font-size: 1.3rem;
    margin: 0;
}

.empty-state {
    background: #f9fafb;
    border-radius: 16px;
    border: 2px dashed #d1d5db;
}

.empty-icon i {
    font-size: 3rem;
    color: #d1d5db;
    margin-bottom: 1rem;
}

.empty-state h3 {
    color: #374151;
    font-size: 1.8rem;
    margin-bottom: 0.75rem;
}

.empty-state p {
    margin-bottom: 2rem;
    font-size: 1.3rem;
}

/* =============================================
   Responsive Design
   ============================================= */

@media (max-width: 1024px) {
    .hotels-grid {
        grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .filters-wrapper {
        padding: 1.5rem;
    }
    
    .filters-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .filters-title {
        font-size: 1.6rem;
    }
    
    .results-info {
        font-size: 1.1rem;
    }
    
    .results-count {
        font-size: 1.2rem;
    }
    
    .filter-controls {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .filter-label {
        font-size: 1rem;
    }
    
    .filter-select {
        font-size: 1rem;
    }
    
    .hotels-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .hotel-name {
        font-size: 1.5rem;
    }
    
    .hotel-location {
        font-size: 1rem;
    }
    
    .distance-info {
        font-size: 0.95rem;
    }
    
    .feature-tag {
        font-size: 0.95rem;
    }
}

@media (max-width: 480px) {
    .filters-title {
        font-size: 1.5rem;
    }
    
    .results-info {
        font-size: 1rem;
    }
    
    .results-count {
        font-size: 1.1rem;
    }
    
    .filter-label {
        font-size: 0.95rem;
    }
    
    .filter-select {
        font-size: 0.95rem;
    }
    
    .hotel-content {
        padding: 1.5rem;
    }
    
    .hotel-name {
        font-size: 1.4rem;
    }
    
    .hotel-location {
        font-size: 0.95rem;
    }
    
    .hotel-actions {
        margin-top: 1rem;
    }
    
    .book-now-btn {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }
    
    .distance-info {
        font-size: 0.9rem;
    }
    
    .feature-tag {
        font-size: 0.9rem;
    }
    
    .hotel-rating {
        font-size: 0.9rem;
    }
    
    .hotel-distance {
        font-size: 0.9rem;
    }
}