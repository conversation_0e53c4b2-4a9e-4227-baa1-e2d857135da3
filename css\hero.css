/* Hero Section Manual Slider Styles */
.hero-section {
    height: 60vh;
    min-height: 400px;
    /* max-height: 700px; */
    overflow: hidden;
    margin-top: 80px;
    position: relative;
    z-index: 1;
}

.hero-slider-container {
    position: relative;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.hero-slides {
    position: relative;
    height: 100%;
    z-index: 1;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    z-index: 1;
}

.hero-slide.active {
    opacity: 1;
    z-index: 2;
}

.hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    display: block;
    background: #f8f9fa;
    position: relative;
    z-index: 1;
}

/* Navigation Arrows */
.slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.8);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 9999 !important;
    transition: all 0.3s ease;
    color: #333;
    font-size: 18px;
    pointer-events: auto !important;
    outline: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.slider-nav:hover {
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.slider-nav:active {
    transform: translateY(-50%) scale(0.95);
}

.prev-btn {
    left: 20px;
}

.next-btn {
    right: 20px;
}

/* Pagination Dots */
.slider-pagination {
    /* position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 10; */
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active,
.dot:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: scale(1.2);
}


