<?php
// ====================================================
// CONTACT FORM HANDLER FOR IMA NATCON 2025
// ENHANCED SECURITY VERSION
// ====================================================
// Simple PHP script for Hostinger Shared Hosting

// ========== CONFIGURATION (CHANGE ONLY THIS) ==========
$your_email = "<EMAIL>"; 
$website_name = "IMA NATCON 2025";
$enable_logging = true; // Set to false to disable logging
$log_file = "contact_logs.txt"; // Log file name
$max_requests_per_hour = 5; // Maximum requests per IP per hour
$blacklist_file = "blacklisted_ips.txt"; // File to store blacklisted IPs
// ========== END OF CONFIGURATION ==========

// Enhanced logging function
function writeLog($message) {
    global $enable_logging, $log_file;
    if ($enable_logging) {
        $timestamp = date('Y-m-d H:i:s');
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
        $log_entry = "[$timestamp] $message | IP: $ip | UA: $user_agent" . PHP_EOL;
        file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    }
}

// Rate limiting function
function checkRateLimit($ip) {
    global $max_requests_per_hour;
    $rate_limit_file = "rate_limit_" . md5($ip) . ".txt";
    $current_time = time();
    $hour_ago = $current_time - 3600;
    
    // Read existing requests
    $requests = [];
    if (file_exists($rate_limit_file)) {
        $requests = array_filter(
            array_map('intval', file($rate_limit_file, FILE_IGNORE_NEW_LINES)),
            function($time) use ($hour_ago) { return $time > $hour_ago; }
        );
    }
    
    // Check if limit exceeded
    if (count($requests) >= $max_requests_per_hour) {
        writeLog("RATE LIMIT EXCEEDED: IP $ip exceeded $max_requests_per_hour requests per hour");
        return false;
    }
    
    // Add current request
    $requests[] = $current_time;
    file_put_contents($rate_limit_file, implode("\n", $requests));
    return true;
}

// IP blacklist check
function isIPBlacklisted($ip) {
    global $blacklist_file;
    if (!file_exists($blacklist_file)) return false;
    $blacklisted = file($blacklist_file, FILE_IGNORE_NEW_LINES);
    return in_array($ip, $blacklisted);
}

// Add IP to blacklist
function blacklistIP($ip) {
    global $blacklist_file;
    file_put_contents($blacklist_file, $ip . PHP_EOL, FILE_APPEND | LOCK_EX);
    writeLog("IP BLACKLISTED: $ip added to blacklist");
}

// Enhanced input sanitization
function sanitizeInput($input) {
    $input = trim($input);
    $input = stripslashes($input);
    $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    return $input;
}

// CSRF token validation
function validateCSRFToken() {
    if (!isset($_POST['csrf_token']) || !isset($_SESSION['csrf_token'])) {
        return false;
    }
    return hash_equals($_SESSION['csrf_token'], $_POST['csrf_token']);
}

// Start session for CSRF protection
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Get client IP
$client_ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';

// Basic security check
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    writeLog("SECURITY: Direct access attempt from IP: $client_ip");
    die('Direct access not allowed');
}

// Check if IP is blacklisted
if (isIPBlacklisted($client_ip)) {
    writeLog("SECURITY: Blacklisted IP attempt: $client_ip");
    die('Access denied');
}

// Rate limiting check
if (!checkRateLimit($client_ip)) {
    writeLog("SECURITY: Rate limit exceeded by IP: $client_ip");
    header("Location: contact-us.html?error=1&msg=" . urlencode("Too many requests. Please try again later."));
    exit;
}

// CSRF protection (optional - uncomment if you want to use it)
// if (!validateCSRFToken()) {
//     writeLog("SECURITY: CSRF token validation failed from IP: $client_ip");
//     header("Location: contact-us.html?error=1&msg=" . urlencode("Security validation failed. Please try again."));
//     exit;
// }

// Get and sanitize form data
$name = isset($_POST['name']) ? sanitizeInput($_POST['name']) : '';
$email = isset($_POST['email']) ? sanitizeInput($_POST['email']) : '';
$phone = isset($_POST['phone']) ? sanitizeInput($_POST['phone']) : '';
$subject = isset($_POST['subject']) ? sanitizeInput($_POST['subject']) : '';
$message = isset($_POST['message']) ? sanitizeInput($_POST['message']) : '';

// Enhanced validation
$errors = array();

if (empty($name) || strlen($name) > 100) {
    $errors[] = "Name is required and must be less than 100 characters";
}

if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL) || strlen($email) > 254) {
    $errors[] = "Valid email is required and must be less than 254 characters";
}

if (empty($phone) || strlen($phone) > 20) {
    $errors[] = "Phone number is required and must be less than 20 characters";
}

if (empty($subject) || strlen($subject) > 200) {
    $errors[] = "Subject is required and must be less than 200 characters";
}

if (empty($message) || strlen($message) > 2000) {
    $errors[] = "Message is required and must be less than 2000 characters";
}

// Check for suspicious content
$suspicious_patterns = ['<script', 'javascript:', 'onload=', 'onerror=', 'eval(', 'document.cookie'];
foreach ($suspicious_patterns as $pattern) {
    if (stripos($message, $pattern) !== false || stripos($subject, $pattern) !== false || stripos($name, $pattern) !== false) {
        writeLog("SECURITY: Suspicious content detected from IP: $client_ip - Pattern: $pattern");
        blacklistIP($client_ip);
        header("Location: contact-us.html?error=1&msg=" . urlencode("Suspicious content detected. Access denied."));
        exit;
    }
}

// If there are errors, redirect back with error
if (!empty($errors)) {
    $error_message = implode(", ", $errors);
    writeLog("VALIDATION ERROR: " . $error_message . " | From: $email | IP: $client_ip");
    header("Location: contact-us.html?error=1&msg=" . urlencode($error_message));
    exit;
}

// Prepare email content with additional security info
$email_subject = "New Contact Form: " . $subject;

$email_body = "
NEW MESSAGE FROM WEBSITE
========================

Name: $name
Email: $email
Phone: $phone
Subject: $subject

Message:
--------
$message

========================
Sent from: $website_name
Date: " . date('Y-m-d H:i:s') . "
IP Address: $client_ip
User Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown') . "
";

// Email headers (important for proper delivery)
$headers = "From: $website_name <$your_email>\r\n";
$headers .= "Reply-To: $email\r\n";
$headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
$headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";

// Send email using PHP mail() function
$mail_sent = mail($your_email, $email_subject, $email_body, $headers);

if ($mail_sent) {
    // Success - redirect back with success message
    writeLog("SUCCESS: Contact form submitted successfully from IP: $client_ip | Email: $email");
    header("Location: contact-us.html?success=1&msg=" . urlencode("Thank you! Your message has been sent successfully."));
} else {
    // Failed - redirect back with error
    writeLog("ERROR: Failed to send email from IP: $client_ip | Email: $email");
    header("Location: contact-us.html?error=1&msg=" . urlencode("Sorry, there was an error sending your message. Please try again."));
}

exit;
?> 