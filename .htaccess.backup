RewriteEngine On

# Priority 1: Allow ALL static files to load directly (no interference)
RewriteCond %{REQUEST_FILENAME} \.(css|js|png|jpg|jpeg|gif|webp|svg|ico|pdf|txt)$ [NC]
RewriteRule .* - [L]

# Priority 2: Handle clean URLs for HTML files only
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME}.html -f
RewriteRule ^([^/]+)/?$ $1.html [L]

# Set index.html as directory index
DirectoryIndex index.html
