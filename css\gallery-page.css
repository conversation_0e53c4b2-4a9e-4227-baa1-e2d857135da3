/* Gallery Page Styles */
.gallery-page {
    min-height: 100vh;
    padding-top: 200px;
    background: linear-gradient(135deg, #f8fffe 0%, #e8f4fd 100%);
}

.gallery-hero {
    text-align: center;
    margin-bottom: 60px;
    padding: 40px 20px;
    background: linear-gradient(135deg, #2e5990, #4a7ba7);
    color: white;
    border-radius: 20px;
    position: relative;
    overflow: hidden;
}

.gallery-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.gallery-hero h1 {
    font-size: 4rem;
    font-weight: 800;
    margin: 0 0 15px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.gallery-hero h1 i {
    font-size: 4.5rem;
    opacity: 0.9;
}

.gallery-hero p {
    font-size: 1.8rem;
    margin: 0 0 20px 0;
    opacity: 0.9;
}

.gallery-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-top: 25px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    display: block;
}

.stat-label {
    font-size: 1.4rem;
    opacity: 0.8;
}

.back-button {
    background: #2e5990;
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 50px;
    font-size: 1.6rem;
    font-weight: 600;
    cursor: pointer;
    margin-bottom: 30px;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(46, 89, 144, 0.3);
}

.back-button:hover {
    background: #1e3f70;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(46, 89, 144, 0.4);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.gallery-item {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.gallery-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.05);
}

.gallery-item-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(46, 89, 144, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.gallery-item:hover .gallery-item-overlay {
    opacity: 1;
}

.gallery-item-overlay i {
    font-size: 4rem;
    color: white;
}

.gallery-item-info {
    padding: 20px;
    text-align: center;
}

.gallery-item-title {
    font-size: 1.6rem;
    font-weight: 600;
    color: #2e5990;
    margin: 0;
}

/* Lightbox Styles */
.lightbox {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(5px);
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    margin: auto;
    display: block;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 10px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.lightbox-close {
    position: absolute;
    top: 20px;
    right: 35px;
    color: white;
    font-size: 4rem;
    font-weight: bold;
    cursor: pointer;
    z-index: 10002;
    transition: all 0.3s ease;
}

.lightbox-close:hover {
    transform: scale(1.2);
    color: #ff6b35;
}

.lightbox-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    font-size: 3rem;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 10px;
    backdrop-filter: blur(10px);
    z-index: 10001;
}

.lightbox-nav:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-50%) scale(1.1);
}

.lightbox-prev {
    left: 30px;
}

.lightbox-next {
    right: 30px;
}

.lightbox-counter {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    font-size: 1.8rem;
    font-weight: 600;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px 20px;
    border-radius: 25px;
    backdrop-filter: blur(10px);
    z-index: 10001;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 25px;
    }
    
    .gallery-hero h1 {
        font-size: 3.5rem;
    }
    
    .back-button {
        left: 20px;
        padding: 12px 18px;
        font-size: 1.4rem;
    }
}

@media (max-width: 768px) {
    .gallery-page {
        padding-top: 160px;
        padding-left: 10px;
        padding-right: 10px;
    }
    
    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 20px;
    }
    
    .gallery-hero {
        margin-bottom: 40px;
        padding: 30px 20px;
        margin-left: 5px;
        margin-right: 5px;
        border-radius: 15px;
    }
    
    .gallery-hero h1 {
        font-size: 2.5rem;
        flex-direction: column;
        gap: 15px;
        line-height: 1.2;
        word-wrap: break-word;
    }
    
    .gallery-hero h1 i {
        font-size: 3rem;
    }
    
    .gallery-hero p {
        font-size: 1.4rem;
        line-height: 1.4;
        padding: 0 10px;
    }
    
    .gallery-stats {
        flex-direction: row;
        gap: 30px;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .back-button {
        font-size: 1.4rem;
        padding: 12px 20px;
        margin-bottom: 20px;
        width: auto;
        justify-content: center;
    }
    
    .lightbox-nav {
        font-size: 2.5rem;
        padding: 18px 15px;
        background: rgba(255, 255, 255, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.5);
        border-radius: 50%;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10001;
    }
    
    .lightbox-prev {
        left: 15px;
    }
    
    .lightbox-next {
        right: 15px;
    }
    
    .lightbox-close {
        font-size: 3rem;
        top: 15px;
        right: 25px;
        background: rgba(0, 0, 0, 0.7);
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10002;
    }
}

@media (max-width: 480px) {
    .gallery-page {
        padding-top: 140px;
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .gallery-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .gallery-item img {
        height: 200px;
    }
    
    .gallery-hero {
        margin-bottom: 30px;
        padding: 25px 15px;
        margin-left: 0;
        margin-right: 0;
        border-radius: 12px;
    }
    
    .gallery-hero h1 {
        font-size: 2rem;
        line-height: 1.1;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
    
    .gallery-hero h1 i {
        font-size: 2.5rem;
    }
    
    .gallery-hero p {
        font-size: 1.3rem;
        line-height: 1.3;
        padding: 0 5px;
    }
    
    .gallery-stats {
        flex-direction: row;
        gap: 20px;
        justify-content: center;
    }
    
    .stat-number {
        font-size: 2.2rem;
    }
    
    .stat-label {
        font-size: 1.2rem;
    }
    
    .back-button {
        font-size: 1.3rem;
        padding: 10px 18px;
        margin-bottom: 15px;
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
    
    .lightbox-nav {
        font-size: 2rem;
        padding: 15px 12px;
        background: rgba(255, 255, 255, 0.4);
        border: 2px solid rgba(255, 255, 255, 0.6);
        border-radius: 50%;
        width: 55px;
        height: 55px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10001;
    }
    
    .lightbox-prev {
        left: 10px;
    }
    
    .lightbox-next {
        right: 10px;
    }
    
    .lightbox-close {
        font-size: 2.5rem;
        top: 10px;
        right: 15px;
        background: rgba(0, 0, 0, 0.8);
        border-radius: 50%;
        width: 45px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10002;
    }
}

/* iPhone SE specific fix - Portrait */
@media only screen 
and (device-width: 375px) 
and (device-height: 667px) 
and (-webkit-device-pixel-ratio: 2)
and (orientation: portrait) {
    .gallery-page {
        padding-top: 170px;
        padding-left: 10px;
        padding-right: 10px;
    }
    
    .gallery-hero {
        margin-bottom: 25px;
        padding: 20px 12px;
        border-radius: 10px;
    }
}

/* iPhone SE 2nd/3rd Gen - Portrait */
@media only screen 
and (device-width: 375px) 
and (device-height: 667px) 
and (-webkit-device-pixel-ratio: 2)
and (orientation: portrait) {
    .gallery-page {
        padding-top: 170px !important;
    }
}

/* iPhone SE viewport based (more reliable) */
@media screen 
and (max-width: 375px) 
and (max-height: 667px) {
    .gallery-page {
        padding-top: 160px;
        padding-left: 10px;
        padding-right: 10px;
    }
}

/* iPhone SE and similar small screens */
@media (max-width: 375px) and (max-height: 667px) {
    .gallery-page {
        padding-top: 150px;
        padding-left: 10px;
        padding-right: 10px;
    }
}

/* Extra small screens (general) */
@media (max-width: 375px) {
    .gallery-page {
        padding-top: 130px;
        padding-left: 10px;
        padding-right: 10px;
    }
    
    .gallery-hero {
        margin-bottom: 25px;
        padding: 20px 12px;
        border-radius: 10px;
    }
    
    .gallery-hero h1 {
        font-size: 1.8rem;
        line-height: 1.1;
        gap: 10px;
    }
    
    .gallery-hero h1 i {
        font-size: 2.2rem;
    }
    
    .gallery-hero p {
        font-size: 1.2rem;
        line-height: 1.3;
        padding: 0;
    }
    
    .gallery-stats {
        gap: 15px;
        margin-top: 20px;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .stat-label {
        font-size: 1.1rem;
    }
    
    .lightbox-nav {
        font-size: 1.8rem;
        padding: 12px 10px;
        background: rgba(255, 255, 255, 0.5);
        border: 2px solid rgba(255, 255, 255, 0.7);
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10001;
    }
    
    .lightbox-prev {
        left: 8px;
    }
    
    .lightbox-next {
        right: 8px;
    }
    
    .lightbox-close {
        font-size: 2.2rem;
        top: 8px;
        right: 12px;
        background: rgba(0, 0, 0, 0.8);
        border-radius: 50%;
        width: 42px;
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10002;
    }
}

/* Very small screens */
@media (max-width: 320px) {
    .gallery-page {
        padding-top: 120px;
        padding-left: 8px;
        padding-right: 8px;
    }
    
    .gallery-hero {
        margin-bottom: 20px;
        padding: 18px 10px;
        border-radius: 8px;
    }
    
    .gallery-hero h1 {
        font-size: 1.6rem;
        line-height: 1.1;
        gap: 8px;
    }
    
    .gallery-hero h1 i {
        font-size: 2rem;
    }
    
    .gallery-hero p {
        font-size: 1.1rem;
        line-height: 1.2;
    }
    
    .gallery-stats {
        gap: 12px;
        margin-top: 15px;
    }
    
    .stat-number {
        font-size: 1.8rem;
    }
    
    .stat-label {
        font-size: 1rem;
    }
    
    .lightbox-nav {
        font-size: 1.6rem;
        padding: 10px 8px;
        background: rgba(255, 255, 255, 0.6);
        border: 2px solid rgba(255, 255, 255, 0.8);
        border-radius: 50%;
        width: 45px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10001;
    }
    
    .lightbox-prev {
        left: 5px;
    }
    
    .lightbox-next {
        right: 5px;
    }
    
    .lightbox-close {
        font-size: 2rem;
        top: 5px;
        right: 8px;
        background: rgba(0, 0, 0, 0.9);
        border-radius: 50%;
        width: 38px;
        height: 38px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10002;
    }
} 